"use client"

import * as React from "react"
import { motion } from "framer-motion"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar"
import { Container, Grid, Flex, Stack } from "@/components/ui/layout"
import { Icons } from "@/components/ui/icon"
import { cn, formatRelativeTime, formatDate, generateAppIcon, generateAvatar } from "@/utils"

interface ActivityDetail {
  id: string
  title: string
  description: string
  longDescription: string
  type: "invitation_code" | "discount" | "trial" | "premium"
  status: "active" | "upcoming" | "ended"
  startDate: string
  endDate: string
  totalCodes: number
  remainingCodes: number
  requirements: {
    title: string
    description: string
    completed: boolean
  }[]
  reward: {
    title: string
    description: string
    value: string
  }
  difficulty: "easy" | "medium" | "hard"
  app: {
    id: string
    name: string
    icon: string
    category: string
  }
  organizer: {
    name: string
    avatar: string
    verified: boolean
  }
  rules: string[]
  faq: {
    question: string
    answer: string
  }[]
}

const mockActivity: ActivityDetail = {
  id: "1",
  title: "新用户专享邀请码",
  description: "首次注册用户可获得3个月免费会员邀请码",
  longDescription: "这是一个专为新用户设计的福利活动。通过完成简单的注册和验证步骤，您就可以获得价值299元的3个月免费会员资格。会员期间可以享受所有高级功能，包括无限制使用、优先客服支持、专属模板等。",
  type: "invitation_code",
  status: "active",
  startDate: "2024-01-01",
  endDate: "2024-12-31",
  totalCodes: 1000,
  remainingCodes: 756,
  requirements: [
    {
      title: "注册新账户",
      description: "使用邮箱或手机号注册新账户",
      completed: false,
    },
    {
      title: "完成邮箱验证",
      description: "点击邮箱中的验证链接完成验证",
      completed: false,
    },
    {
      title: "完善个人资料",
      description: "填写基本个人信息和头像",
      completed: false,
    },
  ],
  reward: {
    title: "3个月免费会员",
    description: "享受所有高级功能，无限制使用",
    value: "价值 ¥299",
  },
  difficulty: "easy",
  app: {
    id: "1",
    name: "Notion",
    icon: generateAppIcon("Notion", "#000000", 64),
    category: "效率工具",
  },
  organizer: {
    name: "Notion官方",
    avatar: generateAvatar("Notion官方", 32),
    verified: true,
  },
  rules: [
    "每个用户只能参与一次此活动",
    "邀请码有效期为获得后30天",
    "邀请码不可转让或出售",
    "活动最终解释权归主办方所有",
  ],
  faq: [
    {
      question: "如何使用获得的邀请码？",
      answer: "获得邀请码后，请在应用内的会员页面输入邀请码即可激活。",
    },
    {
      question: "邀请码有使用期限吗？",
      answer: "是的，邀请码自获得之日起30天内有效，请及时使用。",
    },
    {
      question: "如果遇到问题怎么办？",
      answer: "您可以通过应用内客服或发送邮件到*******************联系我们。",
    },
  ],
}

interface ActivityDetailProps {
  activityId: string
  className?: string
}

const ActivityDetailComponent: React.FC<ActivityDetailProps> = ({ activityId, className }) => {
  const [currentStep, setCurrentStep] = React.useState(0)
  const activity = mockActivity // 实际应用中应该根据activityId获取数据

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "success"
      case "upcoming":
        return "warning"
      case "ended":
        return "secondary"
      default:
        return "secondary"
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case "active":
        return "进行中"
      case "upcoming":
        return "即将开始"
      case "ended":
        return "已结束"
      default:
        return "未知"
    }
  }

  const progress = ((activity.totalCodes - activity.remainingCodes) / activity.totalCodes) * 100
  const completedRequirements = activity.requirements.filter(req => req.completed).length

  return (
    <section className={cn("py-8", className)}>
      <Container>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 左侧：活动详情 */}
          <div className="lg:col-span-2 space-y-8">
            {/* 活动头部 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Card>
                <CardHeader>
                  <div className="flex items-start gap-4">
                    <Avatar size="lg">
                      <AvatarImage src={activity.app.icon} alt={activity.app.name} />
                      <AvatarFallback>
                        {activity.app.name.slice(0, 2).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    
                    <div className="flex-1 space-y-3">
                      <div className="flex items-center gap-3 flex-wrap">
                        <Badge variant={getStatusColor(activity.status) as any}>
                          {getStatusText(activity.status)}
                        </Badge>
                        <Badge variant="outline">{activity.app.category}</Badge>
                      </div>
                      
                      <CardTitle className="text-2xl">{activity.title}</CardTitle>
                      <p className="text-muted-foreground">{activity.description}</p>
                      
                      <div className="flex items-center gap-2">
                        <Avatar size="sm">
                          <AvatarImage src={activity.organizer.avatar} alt={activity.organizer.name} />
                          <AvatarFallback>
                            {activity.organizer.name.slice(0, 2)}
                          </AvatarFallback>
                        </Avatar>
                        <span className="text-sm text-muted-foreground">
                          {activity.organizer.name}
                        </span>
                        {activity.organizer.verified && (
                          <Icons.checkCircle className="w-4 h-4 text-success" />
                        )}
                      </div>
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-6">
                  <p className="text-muted-foreground leading-relaxed">
                    {activity.longDescription}
                  </p>
                  
                  {/* 奖励信息 */}
                  <div className="bg-primary/5 rounded-lg p-4">
                    <div className="flex items-center gap-3">
                      <Icons.gift className="w-6 h-6 text-primary" />
                      <div>
                        <h3 className="font-semibold text-primary">{activity.reward.title}</h3>
                        <p className="text-sm text-muted-foreground">{activity.reward.description}</p>
                        <p className="text-sm font-medium text-primary">{activity.reward.value}</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* 参与步骤 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle>参与步骤</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {activity.requirements.map((requirement, index) => (
                      <div
                        key={index}
                        className={cn(
                          "flex items-start gap-4 p-4 rounded-lg border transition-colors",
                          requirement.completed 
                            ? "bg-success/5 border-success/20" 
                            : index === currentStep 
                            ? "bg-primary/5 border-primary/20" 
                            : "bg-muted/30"
                        )}
                      >
                        <div className={cn(
                          "flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium",
                          requirement.completed 
                            ? "bg-success text-success-foreground" 
                            : index === currentStep 
                            ? "bg-primary text-primary-foreground" 
                            : "bg-muted text-muted-foreground"
                        )}>
                          {requirement.completed ? (
                            <Icons.check className="w-4 h-4" />
                          ) : (
                            index + 1
                          )}
                        </div>
                        
                        <div className="flex-1">
                          <h4 className="font-medium">{requirement.title}</h4>
                          <p className="text-sm text-muted-foreground mt-1">
                            {requirement.description}
                          </p>
                        </div>
                        
                        {!requirement.completed && index === currentStep && (
                          <Button size="sm">
                            开始
                          </Button>
                        )}
                      </div>
                    ))}
                  </div>
                  
                  <div className="mt-6 pt-6 border-t">
                    <div className="flex items-center justify-between text-sm text-muted-foreground mb-2">
                      <span>完成进度</span>
                      <span>{completedRequirements} / {activity.requirements.length}</span>
                    </div>
                    <div className="w-full bg-muted rounded-full h-2">
                      <div
                        className="bg-primary h-2 rounded-full transition-all duration-300"
                        style={{ width: `${(completedRequirements / activity.requirements.length) * 100}%` }}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* 活动规则 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle>活动规则</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {activity.rules.map((rule, index) => (
                      <li key={index} className="flex items-start gap-2 text-sm">
                        <Icons.check className="w-4 h-4 text-success mt-0.5 flex-shrink-0" />
                        <span>{rule}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </motion.div>

            {/* 常见问题 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle>常见问题</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {activity.faq.map((item, index) => (
                      <div key={index} className="space-y-2">
                        <h4 className="font-medium">{item.question}</h4>
                        <p className="text-sm text-muted-foreground">{item.answer}</p>
                        {index < activity.faq.length - 1 && <div className="border-b" />}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>

          {/* 右侧：活动信息 */}
          <div className="lg:col-span-1">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="sticky top-8 space-y-6"
            >
              <Card>
                <CardHeader>
                  <CardTitle>活动信息</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* 剩余邀请码 */}
                  <div className="text-center space-y-2">
                    <div className="text-3xl font-bold text-primary">
                      {activity.remainingCodes}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      剩余邀请码 / 共{activity.totalCodes}个
                    </div>
                    <div className="w-full bg-muted rounded-full h-2">
                      <div
                        className="bg-primary h-2 rounded-full transition-all duration-300"
                        style={{ width: `${progress}%` }}
                      />
                    </div>
                  </div>

                  {/* 时间信息 */}
                  <div className="space-y-3 pt-4 border-t">
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground">开始时间</span>
                      <span className="font-medium">{formatDate(activity.startDate)}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground">结束时间</span>
                      <span className="font-medium">{formatDate(activity.endDate)}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground">剩余时间</span>
                      <span className="font-medium text-warning">23天</span>
                    </div>
                  </div>

                  {/* 操作按钮 */}
                  <div className="pt-4 space-y-3">
                    {activity.status === "active" ? (
                      <Button className="w-full" size="lg" disabled={activity.remainingCodes === 0}>
                        {activity.remainingCodes === 0 ? "已抢完" : "立即参与"}
                      </Button>
                    ) : activity.status === "upcoming" ? (
                      <Button variant="outline" className="w-full" size="lg">
                        <Icons.bell className="w-4 h-4 mr-2" />
                        提醒我
                      </Button>
                    ) : (
                      <Button variant="secondary" className="w-full" size="lg" disabled>
                        活动已结束
                      </Button>
                    )}
                    
                    <Button variant="outline" className="w-full">
                      <Icons.share className="w-4 h-4 mr-2" />
                      分享活动
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </Container>
    </section>
  )
}

export { ActivityDetailComponent }
