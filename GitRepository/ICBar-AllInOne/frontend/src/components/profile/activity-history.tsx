"use client"

import * as React from "react"
import { motion } from "framer-motion"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar"
import { Container, Grid, Flex, Stack } from "@/components/ui/layout"
import { Icons } from "@/components/ui/icon"
import { cn, formatDate, formatRelativeTime, generateAppIcon } from "@/utils"

interface ActivityRecord {
  id: string
  type: "joined" | "completed" | "failed" | "pending"
  activity: {
    id: string
    title: string
    app: {
      name: string
      icon: string
    }
    reward: string
  }
  status: "success" | "pending" | "failed"
  joinedAt: string
  completedAt?: string
  invitationCode?: string
  progress: {
    current: number
    total: number
    steps: {
      title: string
      completed: boolean
      completedAt?: string
    }[]
  }
}

const mockHistory: ActivityRecord[] = [
  {
    id: "1",
    type: "completed",
    activity: {
      id: "1",
      title: "新用户专享邀请码",
      app: {
        name: "Notion",
        icon: generateAppIcon("Notion", "#000000", 32),
      },
      reward: "3个月免费会员",
    },
    status: "success",
    joinedAt: "2024-01-15",
    completedAt: "2024-01-15",
    invitationCode: "NOTION2024-ABC123",
    progress: {
      current: 3,
      total: 3,
      steps: [
        { title: "注册账户", completed: true, completedAt: "2024-01-15" },
        { title: "邮箱验证", completed: true, completedAt: "2024-01-15" },
        { title: "完善资料", completed: true, completedAt: "2024-01-15" },
      ],
    },
  },
  {
    id: "2",
    type: "pending",
    activity: {
      id: "2",
      title: "学生认证优惠",
      app: {
        name: "Figma",
        icon: generateAppIcon("Figma", "#F24E1E", 32),
      },
      reward: "50%折扣优惠",
    },
    status: "pending",
    joinedAt: "2024-01-18",
    progress: {
      current: 1,
      total: 2,
      steps: [
        { title: "学生身份认证", completed: true, completedAt: "2024-01-18" },
        { title: "上传学生证", completed: false },
      ],
    },
  },
  {
    id: "3",
    type: "failed",
    activity: {
      id: "3",
      title: "社交分享活动",
      app: {
        name: "Spotify",
        icon: generateAppIcon("Spotify", "#1DB954", 32),
      },
      reward: "7天免费试用",
    },
    status: "failed",
    joinedAt: "2024-01-10",
    progress: {
      current: 1,
      total: 2,
      steps: [
        { title: "分享到微博/朋友圈", completed: true, completedAt: "2024-01-10" },
        { title: "获得10个点赞", completed: false },
      ],
    },
  },
]

interface ActivityHistoryProps {
  className?: string
}

const ActivityHistory: React.FC<ActivityHistoryProps> = ({ className }) => {
  const [filter, setFilter] = React.useState<"all" | "completed" | "pending" | "failed">("all")
  const [sortBy, setSortBy] = React.useState<"newest" | "oldest">("newest")

  const filteredHistory = mockHistory
    .filter(record => filter === "all" || record.status === filter)
    .sort((a, b) => {
      const dateA = new Date(a.joinedAt).getTime()
      const dateB = new Date(b.joinedAt).getTime()
      return sortBy === "newest" ? dateB - dateA : dateA - dateB
    })

  const getStatusColor = (status: string) => {
    switch (status) {
      case "success":
        return "success"
      case "pending":
        return "warning"
      case "failed":
        return "error"
      default:
        return "secondary"
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case "success":
        return "已完成"
      case "pending":
        return "进行中"
      case "failed":
        return "已失败"
      default:
        return "未知"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "success":
        return Icons.checkCircle
      case "pending":
        return Icons.clock
      case "failed":
        return Icons.alertCircle
      default:
        return Icons.clock
    }
  }

  return (
    <section className={cn("py-8", className)}>
      <Container>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="space-y-6"
        >
          {/* 标题和筛选 */}
          <div className="flex items-center justify-between flex-wrap gap-4">
            <h2 className="text-2xl font-bold">活动历史</h2>
            
            <div className="flex items-center gap-4">
              {/* 状态筛选 */}
              <div className="flex items-center gap-2">
                {[
                  { value: "all", label: "全部" },
                  { value: "completed", label: "已完成" },
                  { value: "pending", label: "进行中" },
                  { value: "failed", label: "已失败" },
                ].map((option) => (
                  <Button
                    key={option.value}
                    variant={filter === option.value ? "default" : "outline"}
                    size="sm"
                    onClick={() => setFilter(option.value as any)}
                  >
                    {option.label}
                  </Button>
                ))}
              </div>
              
              {/* 排序 */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSortBy(sortBy === "newest" ? "oldest" : "newest")}
              >
                <Icons.arrowUpDown className="w-4 h-4 mr-2" />
                {sortBy === "newest" ? "最新" : "最旧"}
              </Button>
            </div>
          </div>

          {/* 统计概览 */}
          <Grid cols={4} gap="md">
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-primary">
                  {mockHistory.length}
                </div>
                <div className="text-sm text-muted-foreground">总参与</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-success">
                  {mockHistory.filter(r => r.status === "success").length}
                </div>
                <div className="text-sm text-muted-foreground">已完成</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-warning">
                  {mockHistory.filter(r => r.status === "pending").length}
                </div>
                <div className="text-sm text-muted-foreground">进行中</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-error">
                  {mockHistory.filter(r => r.status === "failed").length}
                </div>
                <div className="text-sm text-muted-foreground">已失败</div>
              </CardContent>
            </Card>
          </Grid>

          {/* 活动记录列表 */}
          {filteredHistory.length === 0 ? (
            <Card className="text-center py-12">
              <CardContent>
                <Icons.activity className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-semibold mb-2">暂无记录</h3>
                <p className="text-muted-foreground">
                  {filter === "all" ? "您还没有参与任何活动" : `暂无${getStatusText(filter)}的活动`}
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {filteredHistory.map((record, index) => {
                const StatusIcon = getStatusIcon(record.status)
                const progressPercentage = (record.progress.current / record.progress.total) * 100
                
                return (
                  <motion.div
                    key={record.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                  >
                    <Card variant="interactive">
                      <CardContent className="p-6">
                        <div className="flex items-start gap-4">
                          {/* 应用图标 */}
                          <Avatar>
                            <AvatarImage src={record.activity.app.icon} alt={record.activity.app.name} />
                            <AvatarFallback>
                              {record.activity.app.name.slice(0, 2)}
                            </AvatarFallback>
                          </Avatar>
                          
                          <div className="flex-1 space-y-3">
                            {/* 活动信息 */}
                            <div className="flex items-start justify-between">
                              <div>
                                <h3 className="font-semibold text-lg">{record.activity.title}</h3>
                                <p className="text-muted-foreground">
                                  {record.activity.app.name} • {record.activity.reward}
                                </p>
                              </div>
                              
                              <div className="flex items-center gap-2">
                                <Badge variant={getStatusColor(record.status) as any}>
                                  <StatusIcon className="w-3 h-3 mr-1" />
                                  {getStatusText(record.status)}
                                </Badge>
                              </div>
                            </div>
                            
                            {/* 进度条 */}
                            <div className="space-y-2">
                              <div className="flex items-center justify-between text-sm">
                                <span className="text-muted-foreground">完成进度</span>
                                <span className="font-medium">
                                  {record.progress.current} / {record.progress.total}
                                </span>
                              </div>
                              <div className="w-full bg-muted rounded-full h-2">
                                <div
                                  className={cn(
                                    "h-2 rounded-full transition-all duration-300",
                                    record.status === "success" ? "bg-success" :
                                    record.status === "pending" ? "bg-warning" : "bg-error"
                                  )}
                                  style={{ width: `${progressPercentage}%` }}
                                />
                              </div>
                            </div>
                            
                            {/* 步骤详情 */}
                            <div className="space-y-2">
                              {record.progress.steps.map((step, stepIndex) => (
                                <div
                                  key={stepIndex}
                                  className="flex items-center gap-3 text-sm"
                                >
                                  <div className={cn(
                                    "w-4 h-4 rounded-full flex items-center justify-center",
                                    step.completed 
                                      ? "bg-success text-success-foreground" 
                                      : "bg-muted text-muted-foreground"
                                  )}>
                                    {step.completed ? (
                                      <Icons.check className="w-2 h-2" />
                                    ) : (
                                      <div className="w-1 h-1 bg-current rounded-full" />
                                    )}
                                  </div>
                                  <span className={cn(
                                    step.completed ? "text-foreground" : "text-muted-foreground"
                                  )}>
                                    {step.title}
                                  </span>
                                  {step.completedAt && (
                                    <span className="text-xs text-muted-foreground ml-auto">
                                      {formatRelativeTime(step.completedAt)}
                                    </span>
                                  )}
                                </div>
                              ))}
                            </div>
                            
                            {/* 邀请码显示 */}
                            {record.invitationCode && (
                              <div className="bg-success/5 border border-success/20 rounded-lg p-3">
                                <div className="flex items-center justify-between">
                                  <div>
                                    <div className="text-sm font-medium text-success">获得邀请码</div>
                                    <div className="font-mono text-sm">{record.invitationCode}</div>
                                  </div>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => navigator.clipboard.writeText(record.invitationCode!)}
                                  >
                                    <Icons.copy className="w-4 h-4" />
                                  </Button>
                                </div>
                              </div>
                            )}
                            
                            {/* 时间信息 */}
                            <div className="flex items-center justify-between text-xs text-muted-foreground pt-2 border-t">
                              <span>参与时间：{formatDate(record.joinedAt)}</span>
                              {record.completedAt && (
                                <span>完成时间：{formatDate(record.completedAt)}</span>
                              )}
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                )
              })}
            </div>
          )}
          
          {/* 加载更多 */}
          {filteredHistory.length > 0 && (
            <div className="text-center">
              <Button variant="outline">
                加载更多
                <Icons.chevronDown className="w-4 h-4 ml-2" />
              </Button>
            </div>
          )}
        </motion.div>
      </Container>
    </section>
  )
}

export { ActivityHistory }
