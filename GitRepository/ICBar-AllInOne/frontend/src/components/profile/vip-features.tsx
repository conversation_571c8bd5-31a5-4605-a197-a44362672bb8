"use client"

import * as React from "react"
import { motion } from "framer-motion"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Container, Grid, Flex, Stack } from "@/components/ui/layout"
import { Icons } from "@/components/ui/icon"
import { cn } from "@/utils"

interface VIPPlan {
  id: string
  name: string
  price: {
    monthly: number
    yearly: number
  }
  popular: boolean
  features: string[]
  badge?: string
}

const vipPlans: VIPPlan[] = [
  {
    id: "basic",
    name: "基础会员",
    price: {
      monthly: 19,
      yearly: 199,
    },
    popular: false,
    features: [
      "优先获取邀请码",
      "专属客服支持",
      "无广告体验",
      "活动提前通知",
      "基础数据统计",
    ],
  },
  {
    id: "premium",
    name: "高级会员",
    price: {
      monthly: 39,
      yearly: 399,
    },
    popular: true,
    badge: "最受欢迎",
    features: [
      "包含基础会员所有功能",
      "独家VIP活动",
      "邀请码成功率提升50%",
      "高级数据分析",
      "自定义推荐算法",
      "批量操作工具",
      "API访问权限",
    ],
  },
  {
    id: "enterprise",
    name: "企业会员",
    price: {
      monthly: 99,
      yearly: 999,
    },
    popular: false,
    features: [
      "包含高级会员所有功能",
      "企业级技术支持",
      "定制化解决方案",
      "专属客户经理",
      "白标服务",
      "数据导出功能",
      "团队协作工具",
      "SLA保障",
    ],
  },
]

interface VIPFeature {
  icon: keyof typeof Icons
  title: string
  description: string
  vipOnly: boolean
}

const vipFeatures: VIPFeature[] = [
  {
    icon: "zap",
    title: "优先获取邀请码",
    description: "VIP用户在活动开始时优先获取邀请码，成功率更高",
    vipOnly: true,
  },
  {
    icon: "shield",
    title: "专属客服支持",
    description: "7x24小时专属客服，问题响应时间不超过1小时",
    vipOnly: true,
  },
  {
    icon: "crown",
    title: "独家VIP活动",
    description: "参与仅限VIP用户的专属活动，获得更多优质邀请码",
    vipOnly: true,
  },
  {
    icon: "barChart",
    title: "高级数据分析",
    description: "详细的参与数据分析，帮助您优化活动参与策略",
    vipOnly: true,
  },
  {
    icon: "settings",
    title: "自定义推荐",
    description: "根据您的偏好定制个性化的应用和活动推荐",
    vipOnly: true,
  },
  {
    icon: "users",
    title: "团队协作",
    description: "企业用户可以创建团队，统一管理邀请码和活动",
    vipOnly: true,
  },
]

interface VIPFeaturesProps {
  className?: string
}

const VIPFeatures: React.FC<VIPFeaturesProps> = ({ className }) => {
  const [billingCycle, setBillingCycle] = React.useState<"monthly" | "yearly">("yearly")
  const [selectedPlan, setSelectedPlan] = React.useState<string>("premium")

  return (
    <section className={cn("py-8", className)}>
      <Container>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="space-y-12"
        >
          {/* 标题区域 */}
          <div className="text-center space-y-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              <Badge variant="warning" className="mb-4">
                <Icons.crown className="w-4 h-4 mr-2" />
                VIP会员特权
              </Badge>
              <h2 className="text-3xl md:text-4xl font-bold tracking-tight">
                升级VIP，享受专属特权
              </h2>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                成为VIP会员，获得更多邀请码机会，享受专属服务和独家活动
              </p>
            </motion.div>
          </div>

          {/* VIP特性展示 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <h3 className="text-2xl font-bold text-center mb-8">VIP专享特权</h3>
            <Grid cols={3} gap="lg">
              {vipFeatures.map((feature, index) => {
                const IconComponent = Icons[feature.icon]
                
                return (
                  <motion.div
                    key={feature.title}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.3 + index * 0.1 }}
                  >
                    <Card className="h-full group hover:shadow-lg transition-all duration-300">
                      <CardContent className="p-6 text-center">
                        <div className="w-16 h-16 bg-gradient-to-br from-warning to-warning/80 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                          <IconComponent className="w-8 h-8 text-warning-foreground" />
                        </div>
                        <h4 className="text-lg font-semibold mb-2">{feature.title}</h4>
                        <p className="text-muted-foreground text-sm leading-relaxed">
                          {feature.description}
                        </p>
                        {feature.vipOnly && (
                          <Badge variant="warning" size="sm" className="mt-3">
                            <Icons.crown className="w-3 h-3 mr-1" />
                            VIP专享
                          </Badge>
                        )}
                      </CardContent>
                    </Card>
                  </motion.div>
                )
              })}
            </Grid>
          </motion.div>

          {/* 价格方案 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="space-y-8"
          >
            <div className="text-center space-y-4">
              <h3 className="text-2xl font-bold">选择适合您的方案</h3>
              
              {/* 计费周期切换 */}
              <div className="flex items-center justify-center gap-4">
                <span className={cn(
                  "text-sm",
                  billingCycle === "monthly" ? "text-foreground" : "text-muted-foreground"
                )}>
                  按月付费
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setBillingCycle(billingCycle === "monthly" ? "yearly" : "monthly")}
                  className="relative"
                >
                  <div className={cn(
                    "w-12 h-6 bg-muted rounded-full relative transition-colors",
                    billingCycle === "yearly" && "bg-primary"
                  )}>
                    <div className={cn(
                      "w-5 h-5 bg-background rounded-full absolute top-0.5 transition-transform",
                      billingCycle === "yearly" ? "translate-x-6" : "translate-x-0.5"
                    )} />
                  </div>
                </Button>
                <span className={cn(
                  "text-sm",
                  billingCycle === "yearly" ? "text-foreground" : "text-muted-foreground"
                )}>
                  按年付费
                </span>
                {billingCycle === "yearly" && (
                  <Badge variant="success" size="sm">
                    节省20%
                  </Badge>
                )}
              </div>
            </div>

            {/* 价格卡片 */}
            <Grid cols={3} gap="lg">
              {vipPlans.map((plan, index) => (
                <motion.div
                  key={plan.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.5 + index * 0.1 }}
                >
                  <Card className={cn(
                    "relative h-full transition-all duration-300",
                    plan.popular 
                      ? "border-primary shadow-lg scale-105" 
                      : "hover:shadow-md hover:scale-102"
                  )}>
                    {plan.popular && (
                      <div className="absolute -top-3 left-1/2 -translate-x-1/2">
                        <Badge variant="default" className="px-4 py-1">
                          {plan.badge}
                        </Badge>
                      </div>
                    )}
                    
                    <CardHeader className="text-center pb-4">
                      <CardTitle className="text-xl">{plan.name}</CardTitle>
                      <div className="space-y-2">
                        <div className="text-3xl font-bold">
                          ¥{billingCycle === "monthly" ? plan.price.monthly : plan.price.yearly}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {billingCycle === "monthly" ? "每月" : "每年"}
                        </div>
                        {billingCycle === "yearly" && (
                          <div className="text-xs text-success">
                            相当于每月 ¥{Math.round(plan.price.yearly / 12)}
                          </div>
                        )}
                      </div>
                    </CardHeader>
                    
                    <CardContent className="space-y-6">
                      <ul className="space-y-3">
                        {plan.features.map((feature, featureIndex) => (
                          <li key={featureIndex} className="flex items-start gap-2 text-sm">
                            <Icons.check className="w-4 h-4 text-success mt-0.5 flex-shrink-0" />
                            <span>{feature}</span>
                          </li>
                        ))}
                      </ul>
                      
                      <Button 
                        className="w-full" 
                        variant={plan.popular ? "default" : "outline"}
                        size="lg"
                      >
                        {plan.popular ? "立即升级" : "选择方案"}
                      </Button>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </Grid>
          </motion.div>

          {/* 常见问题 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="text-center">常见问题</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium mb-2">VIP会员有什么特权？</h4>
                      <p className="text-sm text-muted-foreground">
                        VIP会员可以优先获取邀请码，参与独家活动，享受专属客服支持等特权。
                      </p>
                    </div>
                    <div>
                      <h4 className="font-medium mb-2">可以随时取消订阅吗？</h4>
                      <p className="text-sm text-muted-foreground">
                        是的，您可以随时在个人设置中取消订阅，取消后会员权益将在当前周期结束后停止。
                      </p>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium mb-2">支持哪些支付方式？</h4>
                      <p className="text-sm text-muted-foreground">
                        我们支持微信支付、支付宝、银行卡等多种支付方式。
                      </p>
                    </div>
                    <div>
                      <h4 className="font-medium mb-2">企业用户有特殊优惠吗？</h4>
                      <p className="text-sm text-muted-foreground">
                        企业用户可以联系我们的销售团队，我们提供定制化的企业解决方案和优惠价格。
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </motion.div>
      </Container>
    </section>
  )
}

export { VIPFeatures }
