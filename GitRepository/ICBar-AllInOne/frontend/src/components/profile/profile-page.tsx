"use client"

import * as React from "react"
import { motion } from "framer-motion"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar"
import { Input } from "@/components/ui/input"
import { Container, Grid, Flex, Stack } from "@/components/ui/layout"
import { Icons } from "@/components/ui/icon"
import { useToast } from "@/components/ui/toast"
import { cn, formatDate, formatRelativeTime, generateAvatar } from "@/utils"

interface UserProfile {
  id: string
  name: string
  email: string
  avatar: string
  bio: string
  joinDate: string
  lastActive: string
  verified: boolean
  vip: boolean
  level: number
  points: number
  stats: {
    totalActivities: number
    completedActivities: number
    totalCodes: number
    favoriteApps: number
  }
  badges: {
    id: string
    name: string
    description: string
    icon: string
    earnedAt: string
  }[]
  recentActivities: {
    id: string
    type: "joined_activity" | "got_code" | "shared" | "reviewed"
    title: string
    description: string
    timestamp: string
  }[]
}

const mockProfile: UserProfile = {
  id: "1",
  name: "张三",
  email: "<EMAIL>",
  avatar: generateAvatar("张三", 128),
  bio: "热爱尝试新应用的科技爱好者，喜欢分享使用心得。",
  joinDate: "2023-06-15",
  lastActive: "2024-01-20",
  verified: true,
  vip: true,
  level: 8,
  points: 2580,
  stats: {
    totalActivities: 45,
    completedActivities: 38,
    totalCodes: 52,
    favoriteApps: 23,
  },
  badges: [
    {
      id: "1",
      name: "早期用户",
      description: "平台前100名注册用户",
      icon: "🌟",
      earnedAt: "2023-06-15",
    },
    {
      id: "2",
      name: "活跃分享者",
      description: "分享超过20个应用",
      icon: "📢",
      earnedAt: "2023-08-20",
    },
    {
      id: "3",
      name: "邀请码达人",
      description: "获得超过50个邀请码",
      icon: "🎯",
      earnedAt: "2023-12-10",
    },
  ],
  recentActivities: [
    {
      id: "1",
      type: "got_code",
      title: "获得Notion邀请码",
      description: "完成新用户专享活动",
      timestamp: "2024-01-20",
    },
    {
      id: "2",
      type: "joined_activity",
      title: "参与Figma学生优惠",
      description: "正在进行身份验证",
      timestamp: "2024-01-19",
    },
    {
      id: "3",
      type: "shared",
      title: "分享了Spotify活动",
      description: "分享到微博获得额外奖励",
      timestamp: "2024-01-18",
    },
  ],
}

interface ProfilePageProps {
  className?: string
}

const ProfilePage: React.FC<ProfilePageProps> = ({ className }) => {
  const [isEditing, setIsEditing] = React.useState(false)
  const [editData, setEditData] = React.useState({
    name: mockProfile.name,
    bio: mockProfile.bio,
  })
  const [isLoading, setIsLoading] = React.useState(false)
  const { success, error } = useToast()

  const handleSave = async () => {
    setIsLoading(true)
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      success("保存成功", "个人资料已更新")
      setIsEditing(false)
    } catch (err) {
      error("保存失败", "请稍后重试")
    } finally {
      setIsLoading(false)
    }
  }

  const getActivityIcon = (type: string) => {
    switch (type) {
      case "joined_activity":
        return Icons.userPlus
      case "got_code":
        return Icons.code
      case "shared":
        return Icons.share
      case "reviewed":
        return Icons.star
      default:
        return Icons.activity
    }
  }

  const getActivityColor = (type: string) => {
    switch (type) {
      case "joined_activity":
        return "text-blue-500"
      case "got_code":
        return "text-green-500"
      case "shared":
        return "text-purple-500"
      case "reviewed":
        return "text-yellow-500"
      default:
        return "text-muted-foreground"
    }
  }

  return (
    <section className={cn("py-8", className)}>
      <Container>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 左侧：个人信息 */}
          <div className="lg:col-span-2 space-y-6">
            {/* 基本信息卡片 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>个人资料</CardTitle>
                    <Button
                      variant={isEditing ? "default" : "outline"}
                      size="sm"
                      onClick={() => {
                        if (isEditing) {
                          handleSave()
                        } else {
                          setIsEditing(true)
                        }
                      }}
                      loading={isLoading}
                    >
                      {isEditing ? (
                        <>
                          <Icons.check className="w-4 h-4 mr-2" />
                          保存
                        </>
                      ) : (
                        <>
                          <Icons.edit className="w-4 h-4 mr-2" />
                          编辑
                        </>
                      )}
                    </Button>
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-6">
                  <div className="flex items-start gap-6">
                    <div className="relative">
                      <Avatar size="2xl">
                        <AvatarImage src={mockProfile.avatar} alt={mockProfile.name} />
                        <AvatarFallback className="text-2xl">
                          {mockProfile.name.slice(0, 2)}
                        </AvatarFallback>
                      </Avatar>
                      {isEditing && (
                        <Button
                          size="icon"
                          variant="outline"
                          className="absolute -bottom-2 -right-2 h-8 w-8 rounded-full"
                        >
                          <Icons.camera className="w-4 h-4" />
                        </Button>
                      )}
                    </div>
                    
                    <div className="flex-1 space-y-4">
                      <div className="flex items-center gap-3">
                        {isEditing ? (
                          <Input
                            value={editData.name}
                            onChange={(e) => setEditData(prev => ({ ...prev, name: e.target.value }))}
                            className="text-2xl font-bold h-auto py-1 px-2"
                          />
                        ) : (
                          <h2 className="text-2xl font-bold">{mockProfile.name}</h2>
                        )}
                        
                        <div className="flex items-center gap-2">
                          {mockProfile.verified && (
                            <Badge variant="success" size="sm">
                              <Icons.checkCircle className="w-3 h-3 mr-1" />
                              已验证
                            </Badge>
                          )}
                          {mockProfile.vip && (
                            <Badge variant="warning" size="sm">
                              <Icons.star className="w-3 h-3 mr-1" />
                              VIP
                            </Badge>
                          )}
                          <Badge variant="outline" size="sm">
                            Lv.{mockProfile.level}
                          </Badge>
                        </div>
                      </div>
                      
                      <div className="text-muted-foreground">
                        <div className="flex items-center gap-2 mb-1">
                          <Icons.mail className="w-4 h-4" />
                          {mockProfile.email}
                        </div>
                        <div className="flex items-center gap-2">
                          <Icons.calendar className="w-4 h-4" />
                          加入于 {formatDate(mockProfile.joinDate)}
                        </div>
                      </div>
                      
                      <div>
                        {isEditing ? (
                          <textarea
                            value={editData.bio}
                            onChange={(e) => setEditData(prev => ({ ...prev, bio: e.target.value }))}
                            className="w-full min-h-[80px] px-3 py-2 border border-input rounded-md bg-background text-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 resize-none"
                            placeholder="介绍一下自己..."
                          />
                        ) : (
                          <p className="text-muted-foreground">
                            {mockProfile.bio || "这个人很懒，什么都没有留下..."}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* 统计数据 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle>活动统计</CardTitle>
                </CardHeader>
                <CardContent>
                  <Grid cols={4} gap="md">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary">{mockProfile.stats.totalActivities}</div>
                      <div className="text-sm text-muted-foreground">参与活动</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-success">{mockProfile.stats.completedActivities}</div>
                      <div className="text-sm text-muted-foreground">完成活动</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-warning">{mockProfile.stats.totalCodes}</div>
                      <div className="text-sm text-muted-foreground">获得邀请码</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-info">{mockProfile.stats.favoriteApps}</div>
                      <div className="text-sm text-muted-foreground">收藏应用</div>
                    </div>
                  </Grid>
                </CardContent>
              </Card>
            </motion.div>

            {/* 成就徽章 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle>成就徽章</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {mockProfile.badges.map((badge, index) => (
                      <motion.div
                        key={badge.id}
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.3, delay: index * 0.1 }}
                        className="flex items-center gap-3 p-3 border rounded-lg hover:bg-muted/50 transition-colors"
                      >
                        <div className="text-2xl">{badge.icon}</div>
                        <div className="flex-1">
                          <h4 className="font-medium">{badge.name}</h4>
                          <p className="text-sm text-muted-foreground">{badge.description}</p>
                          <p className="text-xs text-muted-foreground mt-1">
                            获得于 {formatDate(badge.earnedAt)}
                          </p>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>

          {/* 右侧：最近活动 */}
          <div className="lg:col-span-1">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="sticky top-8 space-y-6"
            >
              <Card>
                <CardHeader>
                  <CardTitle>积分信息</CardTitle>
                </CardHeader>
                <CardContent className="text-center space-y-4">
                  <div>
                    <div className="text-3xl font-bold text-primary">{mockProfile.points}</div>
                    <div className="text-sm text-muted-foreground">当前积分</div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>距离下一级</span>
                      <span>320 积分</span>
                    </div>
                    <div className="w-full bg-muted rounded-full h-2">
                      <div className="bg-primary h-2 rounded-full w-3/4" />
                    </div>
                  </div>
                  <Button variant="outline" size="sm" className="w-full">
                    <Icons.gift className="w-4 h-4 mr-2" />
                    积分商城
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>最近活动</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {mockProfile.recentActivities.map((activity, index) => {
                      const IconComponent = getActivityIcon(activity.type)
                      const iconColor = getActivityColor(activity.type)
                      
                      return (
                        <motion.div
                          key={activity.id}
                          initial={{ opacity: 0, x: 20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.3, delay: index * 0.1 }}
                          className="flex items-start gap-3"
                        >
                          <div className={cn("p-2 rounded-lg bg-background", iconColor)}>
                            <IconComponent className="w-4 h-4" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <h4 className="text-sm font-medium">{activity.title}</h4>
                            <p className="text-xs text-muted-foreground">{activity.description}</p>
                            <p className="text-xs text-muted-foreground mt-1">
                              {formatRelativeTime(activity.timestamp)}
                            </p>
                          </div>
                        </motion.div>
                      )
                    })}
                  </div>
                  <Button variant="outline" size="sm" className="w-full mt-4">
                    查看全部
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </Container>
    </section>
  )
}

export { ProfilePage }
