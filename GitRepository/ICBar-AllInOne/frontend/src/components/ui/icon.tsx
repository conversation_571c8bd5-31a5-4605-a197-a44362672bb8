import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/utils"
import {
  // 导航图标
  Home,
  Search,
  User,
  Settings,
  Menu,
  X,
  ChevronDown,
  ChevronUp,
  ChevronLeft,
  ChevronRight,
  ArrowLeft,
  ArrowRight,
  ArrowUpDown,
  
  // 功能图标
  Download,
  Upload,
  Share,
  Heart,
  Star,
  Bookmark,
  Eye,
  EyeOff,
  Edit,
  Trash2,
  Copy,
  Check,
  Plus,
  Minus,
  
  // 状态图标
  CheckCircle,
  AlertCircle,
  Info,
  AlertTriangle,
  Clock,
  Calendar,
  MapPin,
  Phone,
  Mail,
  Globe,
  
  // 媒体图标
  Play,
  Pause,
  Volume2,
  VolumeX,
  Image,
  Video,
  Music,
  File,
  FileText,
  
  // 社交图标
  MessageCircle,
  Send,
  ThumbsUp,
  ThumbsDown,
  Users,
  UserPlus,
  
  // 商业图标
  ShoppingCart,
  CreditCard,
  DollarSign,
  TrendingUp,
  TrendingDown,
  BarChart3,
  PieChart,
  
  // 系统图标
  Loader2,
  RefreshCw,
  Power,
  Wifi,
  WifiOff,
  Battery,
  Signal,
  Shield,
  Bell,
  Flag,
  Gift,
  Lock,
  Key,
  Lightbulb,
  Camera,
  Zap,
  Crown,
  Github,
  Filter,

  type LucideIcon,
} from "lucide-react"

const iconVariants = cva(
  "inline-flex items-center justify-center",
  {
    variants: {
      size: {
        xs: "h-3 w-3",
        sm: "h-4 w-4",
        default: "h-5 w-5",
        lg: "h-6 w-6",
        xl: "h-8 w-8",
        "2xl": "h-10 w-10",
      },
      variant: {
        default: "text-current",
        primary: "text-primary",
        secondary: "text-secondary",
        muted: "text-muted-foreground",
        success: "text-success",
        warning: "text-warning",
        error: "text-error",
        info: "text-info",
      },
    },
    defaultVariants: {
      size: "default",
      variant: "default",
    },
  }
)

export interface IconProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof iconVariants> {
  icon: LucideIcon
  spinning?: boolean
}

const Icon = React.forwardRef<HTMLDivElement, IconProps>(
  ({ className, size, variant, icon: IconComponent, spinning = false, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(iconVariants({ size, variant, className }), spinning && "animate-spin")}
      {...props}
    >
      <IconComponent className="h-full w-full" />
    </div>
  )
)
Icon.displayName = "Icon"

// 预定义的图标组件
export const Icons = {
  // 导航
  home: Home,
  search: Search,
  user: User,
  settings: Settings,
  menu: Menu,
  close: X,
  chevronDown: ChevronDown,
  chevronUp: ChevronUp,
  chevronLeft: ChevronLeft,
  chevronRight: ChevronRight,
  arrowLeft: ArrowLeft,
  arrowRight: ArrowRight,
  arrowUpDown: ArrowUpDown,
  
  // 功能
  download: Download,
  upload: Upload,
  share: Share,
  heart: Heart,
  star: Star,
  bookmark: Bookmark,
  eye: Eye,
  eyeOff: EyeOff,
  edit: Edit,
  trash: Trash2,
  copy: Copy,
  check: Check,
  plus: Plus,
  minus: Minus,
  
  // 状态
  checkCircle: CheckCircle,
  alertCircle: AlertCircle,
  info: Info,
  warning: AlertTriangle,
  clock: Clock,
  calendar: Calendar,
  mapPin: MapPin,
  phone: Phone,
  mail: Mail,
  globe: Globe,
  
  // 媒体
  play: Play,
  pause: Pause,
  volume: Volume2,
  volumeOff: VolumeX,
  image: Image,
  video: Video,
  music: Music,
  file: File,
  fileText: FileText,
  
  // 社交
  message: MessageCircle,
  send: Send,
  thumbsUp: ThumbsUp,
  thumbsDown: ThumbsDown,
  users: Users,
  userPlus: UserPlus,
  
  // 商业
  cart: ShoppingCart,
  creditCard: CreditCard,
  dollar: DollarSign,
  trendingUp: TrendingUp,
  trendingDown: TrendingDown,
  barChart: BarChart3,
  pieChart: PieChart,
  
  // 系统
  loading: Loader2,
  refresh: RefreshCw,
  power: Power,
  wifi: Wifi,
  wifiOff: WifiOff,
  battery: Battery,
  signal: Signal,
  shield: Shield,
  bell: Bell,
  flag: Flag,
  gift: Gift,
  lock: Lock,
  key: Key,
  lightbulb: Lightbulb,
  camera: Camera,
  zap: Zap,
  crown: Crown,
  github: Github,
  filter: Filter,

  // 自定义图标
  logo: ({ className, ...props }: React.SVGProps<SVGSVGElement>) => (
    <svg
      className={className}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <path d="M12 2L2 7l10 5 10-5-10-5z" />
      <path d="M2 17l10 5 10-5" />
      <path d="M2 12l10 5 10-5" />
    </svg>
  ),
  
  // 应用相关图标
  app: ({ className, ...props }: React.SVGProps<SVGSVGElement>) => (
    <svg
      className={className}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <rect x="3" y="3" width="7" height="7" />
      <rect x="14" y="3" width="7" height="7" />
      <rect x="14" y="14" width="7" height="7" />
      <rect x="3" y="14" width="7" height="7" />
    </svg>
  ),
  
  // 邀请码图标
  code: ({ className, ...props }: React.SVGProps<SVGSVGElement>) => (
    <svg
      className={className}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <polyline points="16,18 22,12 16,6" />
      <polyline points="8,6 2,12 8,18" />
    </svg>
  ),
  
  // 活动图标
  activity: ({ className, ...props }: React.SVGProps<SVGSVGElement>) => (
    <svg
      className={className}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <polyline points="22,12 18,12 15,21 9,3 6,12 2,12" />
    </svg>
  ),
}

export { Icon, iconVariants, type LucideIcon }
