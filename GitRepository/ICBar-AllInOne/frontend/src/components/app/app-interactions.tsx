"use client"

import * as React from "react"
import { motion } from "framer-motion"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar"
import { Container, Grid, Flex, Stack } from "@/components/ui/layout"
import { Icons } from "@/components/ui/icon"
import { cn, formatRelativeTime, generateAvatar } from "@/utils"

interface Review {
  id: string
  user: {
    name: string
    avatar: string
  }
  rating: number
  content: string
  createdAt: string
  helpful: number
  verified: boolean
}

interface Screenshot {
  id: string
  url: string
  caption?: string
}

const mockReviews: Review[] = [
  {
    id: "1",
    user: {
      name: "张三",
      avatar: generateAvatar("张三", 32),
    },
    rating: 5,
    content: "非常好用的应用，界面简洁，功能强大。通过邀请码获得了免费会员，体验很棒！",
    createdAt: "2024-01-15",
    helpful: 12,
    verified: true,
  },
  {
    id: "2",
    user: {
      name: "李四",
      avatar: generateAvatar("李四", 32),
    },
    rating: 4,
    content: "整体不错，就是有些功能需要会员才能使用。希望能有更多免费功能。",
    createdAt: "2024-01-10",
    helpful: 8,
    verified: false,
  },
  {
    id: "3",
    user: {
      name: "王五",
      avatar: generateAvatar("王五", 32),
    },
    rating: 5,
    content: "客服响应很快，遇到问题能及时解决。推荐大家使用！",
    createdAt: "2024-01-08",
    helpful: 15,
    verified: true,
  },
]

// 生成截图占位符
const generateScreenshot = (caption: string) => {
  const svg = `
    <svg width="300" height="600" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="grad-${caption}" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
        </linearGradient>
      </defs>
      <rect width="100%" height="100%" fill="url(#grad-${caption})" stroke="#cbd5e1" stroke-width="2" rx="12"/>
      <rect x="20" y="40" width="260" height="40" fill="#3b82f6" rx="8"/>
      <rect x="20" y="100" width="200" height="20" fill="#64748b" rx="4"/>
      <rect x="20" y="140" width="180" height="20" fill="#64748b" rx="4"/>
      <rect x="20" y="180" width="220" height="20" fill="#64748b" rx="4"/>
      <text x="150" y="520" text-anchor="middle" fill="#64748b" font-family="system-ui, sans-serif" font-size="16" font-weight="500">
        ${caption}
      </text>
    </svg>
  `.trim()

  return `data:image/svg+xml,${encodeURIComponent(svg)}`
}

const mockScreenshots: Screenshot[] = [
  {
    id: "1",
    url: generateScreenshot("主界面"),
    caption: "主界面",
  },
  {
    id: "2",
    url: generateScreenshot("功能列表"),
    caption: "功能列表",
  },
  {
    id: "3",
    url: generateScreenshot("设置页面"),
    caption: "设置页面",
  },
  {
    id: "4",
    url: generateScreenshot("数据统计"),
    caption: "数据统计",
  },
]

interface AppInteractionsProps {
  appId: string
  className?: string
}

const AppInteractions: React.FC<AppInteractionsProps> = ({ appId, className }) => {
  const [activeTab, setActiveTab] = React.useState<"screenshots" | "reviews" | "related">("screenshots")

  const tabs = [
    { id: "screenshots", label: "应用截图", icon: Icons.image },
    { id: "reviews", label: "用户评价", icon: Icons.messageCircle },
    { id: "related", label: "相关推荐", icon: Icons.app },
  ]

  return (
    <section className={cn("py-8", className)}>
      <Container>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="space-y-6"
        >
          {/* 标签导航 */}
          <div className="flex items-center gap-4 border-b">
            {tabs.map((tab) => {
              const IconComponent = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={cn(
                    "flex items-center gap-2 px-4 py-3 text-sm font-medium transition-colors relative",
                    activeTab === tab.id
                      ? "text-primary border-b-2 border-primary"
                      : "text-muted-foreground hover:text-foreground"
                  )}
                >
                  <IconComponent className="w-4 h-4" />
                  {tab.label}
                </button>
              )
            })}
          </div>

          {/* 内容区域 */}
          <div className="min-h-[400px]">
            {activeTab === "screenshots" && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className="space-y-6"
              >
                <h3 className="text-xl font-semibold">应用截图</h3>
                <Grid cols={4} gap="md">
                  {mockScreenshots.map((screenshot, index) => (
                    <motion.div
                      key={screenshot.id}
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                      className="group cursor-pointer"
                    >
                      <div className="relative overflow-hidden rounded-lg border bg-muted aspect-[9/16]">
                        <img
                          src={screenshot.url}
                          alt={screenshot.caption}
                          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                        />
                        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300" />
                        <div className="absolute bottom-0 left-0 right-0 p-3 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                          <p className="text-white text-sm font-medium">{screenshot.caption}</p>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </Grid>
              </motion.div>
            )}

            {activeTab === "reviews" && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className="space-y-6"
              >
                <div className="flex items-center justify-between">
                  <h3 className="text-xl font-semibold">用户评价</h3>
                  <Button variant="outline" size="sm">
                    <Icons.edit className="w-4 h-4 mr-2" />
                    写评价
                  </Button>
                </div>

                <div className="space-y-4">
                  {mockReviews.map((review, index) => (
                    <motion.div
                      key={review.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                    >
                      <Card>
                        <CardContent className="p-6">
                          <div className="flex items-start gap-4">
                            <Avatar>
                              <AvatarImage src={review.user.avatar} alt={review.user.name} />
                              <AvatarFallback>
                                {review.user.name.slice(0, 2)}
                              </AvatarFallback>
                            </Avatar>

                            <div className="flex-1 space-y-3">
                              <div className="flex items-center gap-3">
                                <h4 className="font-medium">{review.user.name}</h4>
                                {review.verified && (
                                  <Badge variant="success" size="sm">
                                    <Icons.checkCircle className="w-3 h-3 mr-1" />
                                    已验证
                                  </Badge>
                                )}
                                <div className="flex items-center gap-1">
                                  {Array.from({ length: 5 }).map((_, i) => (
                                    <Icons.star
                                      key={i}
                                      className={cn(
                                        "w-4 h-4",
                                        i < review.rating
                                          ? "text-yellow-500 fill-current"
                                          : "text-muted-foreground"
                                      )}
                                    />
                                  ))}
                                </div>
                                <span className="text-sm text-muted-foreground">
                                  {formatRelativeTime(review.createdAt)}
                                </span>
                              </div>

                              <p className="text-muted-foreground leading-relaxed">
                                {review.content}
                              </p>

                              <div className="flex items-center gap-4">
                                <Button variant="ghost" size="sm">
                                  <Icons.thumbsUp className="w-4 h-4 mr-2" />
                                  有用 ({review.helpful})
                                </Button>
                                <Button variant="ghost" size="sm">
                                  <Icons.messageCircle className="w-4 h-4 mr-2" />
                                  回复
                                </Button>
                                <Button variant="ghost" size="sm">
                                  <Icons.flag className="w-4 h-4 mr-2" />
                                  举报
                                </Button>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  ))}
                </div>

                <div className="text-center">
                  <Button variant="outline">
                    查看更多评价
                    <Icons.chevronDown className="w-4 h-4 ml-2" />
                  </Button>
                </div>
              </motion.div>
            )}

            {activeTab === "related" && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className="space-y-6"
              >
                <h3 className="text-xl font-semibold">相关推荐</h3>
                <div className="text-center py-12">
                  <Icons.app className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
                  <h4 className="text-lg font-semibold mb-2">即将推出</h4>
                  <p className="text-muted-foreground">
                    我们正在为您准备相关应用推荐，敬请期待！
                  </p>
                </div>
              </motion.div>
            )}
          </div>
        </motion.div>
      </Container>
    </section>
  )
}

export { AppInteractions }
