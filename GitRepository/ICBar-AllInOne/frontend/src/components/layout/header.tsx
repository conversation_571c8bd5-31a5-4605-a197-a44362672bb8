"use client"

import * as React from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { motion, AnimatePresence } from "framer-motion"
import { Button } from "@/components/ui/button"
import { Container, Flex } from "@/components/ui/layout"
import { Icons } from "@/components/ui/icon"
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/utils"

interface NavItem {
  label: string
  href: string
  icon?: keyof typeof Icons
  badge?: string
}

const navItems: NavItem[] = [
  {
    label: "首页",
    href: "/",
    icon: "home",
  },
  {
    label: "应用推荐",
    href: "/apps",
    icon: "app",
  },
  {
    label: "送码活动",
    href: "/activities",
    icon: "code",
    badge: "热门",
  },
  {
    label: "社区",
    href: "/community",
    icon: "users",
  },
]

interface HeaderProps {
  className?: string
}

const Header: React.FC<HeaderProps> = ({ className }) => {
  const pathname = usePathname()
  const [mounted, setMounted] = React.useState(false)
  const [isScrolled, setIsScrolled] = React.useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = React.useState(false)
  const [isUserMenuOpen, setIsUserMenuOpen] = React.useState(false)

  // 模拟用户状态
  const [user, setUser] = React.useState<{
    name: string
    email: string
    avatar: string
  } | null>(null)

  React.useEffect(() => {
    setMounted(true)
  }, [])

  React.useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10)
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element
      if (!target.closest('[data-user-menu]')) {
        setIsUserMenuOpen(false)
      }
      if (!target.closest('[data-mobile-menu]')) {
        setIsMobileMenuOpen(false)
      }
    }

    document.addEventListener("click", handleClickOutside)
    return () => document.removeEventListener("click", handleClickOutside)
  }, [])

  if (!mounted) {
    return (
      <header className={cn("sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60", className)}>
        <Container>
          <div className="flex h-16 items-center justify-between">
            <div className="flex items-center gap-6">
              <div className="h-8 w-32 bg-muted animate-pulse rounded" />
              <div className="hidden md:flex items-center gap-6">
                {Array.from({ length: 4 }).map((_, i) => (
                  <div key={i} className="h-4 w-16 bg-muted animate-pulse rounded" />
                ))}
              </div>
            </div>
            <div className="flex items-center gap-4">
              <div className="h-8 w-20 bg-muted animate-pulse rounded" />
              <div className="h-8 w-8 bg-muted animate-pulse rounded-full" />
            </div>
          </div>
        </Container>
      </header>
    )
  }

  return (
    <header className={cn(
      "sticky top-0 z-50 w-full border-b transition-all duration-200",
      isScrolled 
        ? "bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 shadow-sm" 
        : "bg-background",
      className
    )}>
      <Container>
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex items-center gap-2 hover:opacity-80 transition-opacity">
            <Icons.logo className="h-8 w-8 text-primary" />
            <span className="text-xl font-bold bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
              ICBar
            </span>
          </Link>

          {/* 桌面导航 */}
          <nav className="hidden md:flex items-center gap-6">
            {navItems.map((item) => {
              const IconComponent = item.icon ? Icons[item.icon] : null
              const isActive = pathname === item.href

              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={cn(
                    "flex items-center gap-2 text-sm font-medium transition-colors relative group",
                    isActive
                      ? "text-foreground"
                      : "text-muted-foreground hover:text-foreground"
                  )}
                >
                  {IconComponent && <IconComponent className="w-4 h-4" />}
                  <span>{item.label}</span>
                  {item.badge && (
                    <Badge variant="warning" size="sm" className="ml-1">
                      {item.badge}
                    </Badge>
                  )}
                  <div className={cn(
                    "absolute -bottom-1 left-0 h-0.5 bg-primary transition-all duration-200",
                    isActive ? "w-full" : "w-0 group-hover:w-full"
                  )} />
                </Link>
              )
            })}
          </nav>

          {/* 右侧操作区 */}
          <div className="flex items-center gap-4">
            {/* 搜索按钮 */}
            <Button variant="ghost" size="icon" className="hidden sm:flex">
              <Icons.search className="w-4 h-4" />
            </Button>

            {/* 用户菜单或登录按钮 */}
            {user ? (
              <div className="relative" data-user-menu>
                <Button
                  variant="ghost"
                  className="flex items-center gap-2 px-2"
                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                >
                  <Avatar size="sm">
                    <AvatarImage src={user.avatar} alt={user.name} />
                    <AvatarFallback>
                      {user.name.slice(0, 2).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <Icons.chevronDown className={cn(
                    "w-4 h-4 transition-transform duration-200",
                    isUserMenuOpen && "rotate-180"
                  )} />
                </Button>

                <AnimatePresence>
                  {isUserMenuOpen && (
                    <motion.div
                      initial={{ opacity: 0, y: 10, scale: 0.95 }}
                      animate={{ opacity: 1, y: 0, scale: 1 }}
                      exit={{ opacity: 0, y: 10, scale: 0.95 }}
                      transition={{ duration: 0.2 }}
                      className="absolute right-0 top-full mt-2 w-56 bg-background border rounded-lg shadow-lg py-2"
                    >
                      <div className="px-4 py-2 border-b">
                        <p className="font-medium">{user.name}</p>
                        <p className="text-sm text-muted-foreground">{user.email}</p>
                      </div>
                      <Link href="/profile" className="flex items-center gap-2 px-4 py-2 text-sm hover:bg-muted">
                        <Icons.user className="w-4 h-4" />
                        个人中心
                      </Link>
                      <Link href="/settings" className="flex items-center gap-2 px-4 py-2 text-sm hover:bg-muted">
                        <Icons.settings className="w-4 h-4" />
                        设置
                      </Link>
                      <div className="border-t my-2" />
                      <button className="flex items-center gap-2 px-4 py-2 text-sm hover:bg-muted w-full text-left">
                        <Icons.power className="w-4 h-4" />
                        退出登录
                      </button>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            ) : (
              <Button size="sm">
                <Icons.user className="w-4 h-4 mr-2" />
                登录
              </Button>
            )}

            {/* 移动端菜单按钮 */}
            <Button
              variant="ghost"
              size="icon"
              className="md:hidden"
              data-mobile-menu
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            >
              <Icons.menu className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* 移动端导航菜单 */}
        <AnimatePresence>
          {isMobileMenuOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.2 }}
              className="md:hidden border-t bg-background"
              data-mobile-menu
            >
              <nav className="py-4 space-y-2">
                {navItems.map((item) => {
                  const IconComponent = item.icon ? Icons[item.icon] : null
                  const isActive = pathname === item.href

                  return (
                    <Link
                      key={item.href}
                      href={item.href}
                      className={cn(
                        "flex items-center gap-3 px-4 py-2 text-sm font-medium rounded-md transition-colors",
                        isActive
                          ? "text-foreground bg-muted"
                          : "text-muted-foreground hover:text-foreground hover:bg-muted"
                      )}
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      {IconComponent && <IconComponent className="w-4 h-4" />}
                      <span>{item.label}</span>
                      {item.badge && (
                        <Badge variant="warning" size="sm" className="ml-auto">
                          {item.badge}
                        </Badge>
                      )}
                    </Link>
                  )
                })}
              </nav>
            </motion.div>
          )}
        </AnimatePresence>
      </Container>
    </header>
  )
}

export { Header }
