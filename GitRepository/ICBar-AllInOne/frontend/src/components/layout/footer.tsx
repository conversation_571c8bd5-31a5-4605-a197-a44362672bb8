"use client"

import * as React from "react"
import Link from "next/link"
import { motion } from "framer-motion"
import { Button } from "@/components/ui/button"
import { Container, Grid, Flex, Stack, Separator } from "@/components/ui/layout"
import { Icons } from "@/components/ui/icon"
import { cn } from "@/utils"

interface FooterLink {
  label: string
  href: string
}

interface FooterSection {
  title: string
  links: FooterLink[]
}

const footerSections: FooterSection[] = [
  {
    title: "产品",
    links: [
      { label: "应用推荐", href: "/apps" },
      { label: "送码活动", href: "/activities" },
      { label: "VIP会员", href: "/vip" },
      { label: "API文档", href: "/docs" },
    ],
  },
  {
    title: "社区",
    links: [
      { label: "用户论坛", href: "/community" },
      { label: "开发者", href: "/developers" },
      { label: "合作伙伴", href: "/partners" },
      { label: "媒体资源", href: "/media" },
    ],
  },
  {
    title: "支持",
    links: [
      { label: "帮助中心", href: "/help" },
      { label: "联系我们", href: "/contact" },
      { label: "意见反馈", href: "/feedback" },
      { label: "状态页面", href: "/status" },
    ],
  },
  {
    title: "公司",
    links: [
      { label: "关于我们", href: "/about" },
      { label: "招聘信息", href: "/careers" },
      { label: "新闻动态", href: "/news" },
      { label: "投资者", href: "/investors" },
    ],
  },
]

const socialLinks = [
  { icon: Icons.message, href: "#", label: "微信" },
  { icon: Icons.share, href: "#", label: "微博" },
  { icon: Icons.github, href: "#", label: "GitHub" },
  { icon: Icons.mail, href: "#", label: "邮箱" },
]

interface FooterProps {
  className?: string
}

const Footer: React.FC<FooterProps> = ({ className }) => {
  const currentYear = new Date().getFullYear()

  return (
    <footer className={cn("bg-muted/30 border-t", className)}>
      <Container>
        <div className="py-12 lg:py-16">
          {/* 主要内容区域 */}
          <Grid cols={5} gap="lg" className="mb-12">
            {/* 品牌信息 */}
            <div className="lg:col-span-1">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5 }}
                className="space-y-4"
              >
                <Link href="/" className="flex items-center gap-2">
                  <Icons.logo className="h-8 w-8 text-primary" />
                  <span className="text-xl font-bold bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
                    ICBar
                  </span>
                </Link>
                
                <p className="text-sm text-muted-foreground leading-relaxed">
                  专业的应用推荐平台，汇聚优质应用推荐，提供专属邀请码服务。让你轻松发现好应用，享受专属优惠。
                </p>
                
                {/* 社交媒体链接 */}
                <div className="flex items-center gap-3">
                  {socialLinks.map((social, index) => {
                    const IconComponent = social.icon
                    return (
                      <motion.div
                        key={social.label}
                        initial={{ opacity: 0, scale: 0.8 }}
                        whileInView={{ opacity: 1, scale: 1 }}
                        viewport={{ once: true }}
                        transition={{ duration: 0.3, delay: index * 0.1 }}
                      >
                        <Link href={social.href} aria-label={social.label}>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 hover:bg-primary/10 hover:text-primary"
                          >
                            <IconComponent className="h-4 w-4" />
                          </Button>
                        </Link>
                      </motion.div>
                    )
                  })}
                </div>
              </motion.div>
            </div>

            {/* 链接区域 */}
            <div className="lg:col-span-4">
              <Grid cols={4} gap="lg">
                {footerSections.map((section, sectionIndex) => (
                  <motion.div
                    key={section.title}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: sectionIndex * 0.1 }}
                  >
                    <Stack spacing="md">
                      <h3 className="font-semibold text-foreground">{section.title}</h3>
                      <Stack spacing="sm">
                        {section.links.map((link, linkIndex) => (
                          <motion.div
                            key={link.href}
                            initial={{ opacity: 0, x: -10 }}
                            whileInView={{ opacity: 1, x: 0 }}
                            viewport={{ once: true }}
                            transition={{ 
                              duration: 0.3, 
                              delay: sectionIndex * 0.1 + linkIndex * 0.05 
                            }}
                          >
                            <Link
                              href={link.href}
                              className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                            >
                              {link.label}
                            </Link>
                          </motion.div>
                        ))}
                      </Stack>
                    </Stack>
                  </motion.div>
                ))}
              </Grid>
            </div>
          </Grid>

          {/* 分隔线 */}
          <Separator className="mb-8" />

          {/* 底部信息 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <Flex justify="between" align="center" className="flex-col md:flex-row gap-4">
              <div className="flex items-center gap-6 text-sm text-muted-foreground">
                <span>&copy; {currentYear} ICBar. 保留所有权利.</span>
                <Link href="/privacy" className="hover:text-foreground transition-colors">
                  隐私政策
                </Link>
                <Link href="/terms" className="hover:text-foreground transition-colors">
                  服务条款
                </Link>
                <Link href="/cookies" className="hover:text-foreground transition-colors">
                  Cookie政策
                </Link>
              </div>
              
              <div className="flex items-center gap-4">
                <span className="text-sm text-muted-foreground">
                  Made with ❤️ in China
                </span>
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <div className="w-2 h-2 bg-success rounded-full animate-pulse" />
                  <span>服务正常</span>
                </div>
              </div>
            </Flex>
          </motion.div>
        </div>
      </Container>
    </footer>
  )
}

export { Footer }
