import { Header } from "@/components/layout/header"
import { Footer } from "@/components/layout/footer"
import { Container, Grid } from "@/components/ui/layout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Icons } from "@/components/ui/icon"

export default function AppsPage() {
  const apps = [
    {
      id: 1,
      name: "Notion",
      description: "一体化工作空间，集笔记、任务管理、数据库于一身",
      category: "效率工具",
      rating: 4.8,
      downloads: "10M+",
      image: "/api/placeholder/80/80",
      tags: ["笔记", "协作", "数据库"],
      featured: true
    },
    {
      id: 2,
      name: "Figma",
      description: "协作式界面设计工具，支持实时多人编辑",
      category: "设计工具",
      rating: 4.9,
      downloads: "5M+",
      image: "/api/placeholder/80/80",
      tags: ["设计", "协作", "原型"],
      featured: true
    },
    {
      id: 3,
      name: "Discord",
      description: "为游戏玩家和社区打造的语音、视频和文字聊天应用",
      category: "社交通讯",
      rating: 4.7,
      downloads: "100M+",
      image: "/api/placeholder/80/80",
      tags: ["聊天", "语音", "社区"],
      featured: false
    },
    {
      id: 4,
      name: "Spotify",
      description: "全球领先的音乐流媒体平台",
      category: "音乐娱乐",
      rating: 4.6,
      downloads: "1B+",
      image: "/api/placeholder/80/80",
      tags: ["音乐", "播客", "流媒体"],
      featured: false
    }
  ]

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="py-8">
        <Container>
          {/* 页面标题 */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-2">应用推荐</h1>
            <p className="text-muted-foreground">
              发现优质应用，提升工作和生活效率
            </p>
          </div>

          {/* 筛选和搜索 */}
          <div className="flex flex-col sm:flex-row gap-4 mb-8">
            <div className="flex gap-2 flex-wrap">
              <Button variant="default" size="sm">全部</Button>
              <Button variant="outline" size="sm">效率工具</Button>
              <Button variant="outline" size="sm">设计工具</Button>
              <Button variant="outline" size="sm">社交通讯</Button>
              <Button variant="outline" size="sm">音乐娱乐</Button>
            </div>
            <div className="flex gap-2 ml-auto">
              <Button variant="outline" size="sm">
                <Icons.filter className="w-4 h-4 mr-2" />
                筛选
              </Button>
              <Button variant="outline" size="sm">
                <Icons.search className="w-4 h-4 mr-2" />
                搜索
              </Button>
            </div>
          </div>

          {/* 应用列表 */}
          <Grid cols={1} gap="md" className="md:grid-cols-2 lg:grid-cols-3">
            {apps.map((app) => (
              <Card key={app.id} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-4">
                  <div className="flex items-start gap-4">
                    <img 
                      src={app.image} 
                      alt={app.name}
                      className="w-16 h-16 rounded-lg object-cover"
                    />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <CardTitle className="text-lg truncate">{app.name}</CardTitle>
                        {app.featured && (
                          <Badge variant="warning" size="sm">推荐</Badge>
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">
                        {app.category}
                      </p>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Icons.star className="w-4 h-4 fill-warning text-warning" />
                          <span>{app.rating}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Icons.download className="w-4 h-4" />
                          <span>{app.downloads}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent className="pt-0">
                  <p className="text-sm text-muted-foreground mb-4 line-clamp-2">
                    {app.description}
                  </p>
                  
                  <div className="flex flex-wrap gap-1 mb-4">
                    {app.tags.map((tag) => (
                      <Badge key={tag} variant="secondary" size="sm">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                  
                  <div className="flex gap-2">
                    <Button className="flex-1" size="sm">
                      <Icons.download className="w-4 h-4 mr-2" />
                      获取邀请码
                    </Button>
                    <Button variant="outline" size="sm">
                      <Icons.eye className="w-4 h-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </Grid>

          {/* 加载更多 */}
          <div className="text-center mt-8">
            <Button variant="outline">
              加载更多应用
            </Button>
          </div>
        </Container>
      </main>

      <Footer />
    </div>
  )
}
