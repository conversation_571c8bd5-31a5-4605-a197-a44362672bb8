"use client"

import { useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Container, Center } from "@/components/ui/layout"
import { Icons } from "@/components/ui/icon"

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    // 记录错误到错误报告服务
    console.error(error)
  }, [error])

  return (
    <Container className="min-h-screen">
      <Center className="min-h-screen">
        <Card className="w-full max-w-md text-center">
          <CardHeader>
            <div className="w-16 h-16 bg-error/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <Icons.alertCircle className="w-8 h-8 text-error" />
            </div>
            <CardTitle className="text-xl">出现了一些问题</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-muted-foreground">
              很抱歉，页面加载时出现了错误。请稍后重试。
            </p>
            <div className="flex gap-3">
              <Button onClick={reset} className="flex-1">
                <Icons.refresh className="w-4 h-4 mr-2" />
                重试
              </Button>
              <Button variant="outline" onClick={() => window.location.href = "/"} className="flex-1">
                <Icons.home className="w-4 h-4 mr-2" />
                返回首页
              </Button>
            </div>
          </CardContent>
        </Card>
      </Center>
    </Container>
  )
}
