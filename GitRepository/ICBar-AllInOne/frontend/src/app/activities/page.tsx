import { Header } from "@/components/layout/header"
import { Footer } from "@/components/layout/footer"
import { Container, Grid } from "@/components/ui/layout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Icons } from "@/components/ui/icon"

export default function ActivitiesPage() {
  const activities = [
    {
      id: 1,
      title: "Notion Pro 年度会员限时免费",
      description: "获取Notion Pro年度会员资格，享受无限制的团队协作功能",
      app: "Notion",
      type: "会员福利",
      status: "进行中",
      endDate: "2024-02-28",
      participants: 1234,
      maxParticipants: 5000,
      image: "/api/placeholder/300/200",
      hot: true
    },
    {
      id: 2,
      title: "Figma 专业版邀请码发放",
      description: "限量发放Figma专业版邀请码，先到先得",
      app: "Figma",
      type: "邀请码",
      status: "进行中",
      endDate: "2024-02-25",
      participants: 856,
      maxParticipants: 1000,
      image: "/api/placeholder/300/200",
      hot: true
    },
    {
      id: 3,
      title: "Discord Nitro 免费体验",
      description: "体验Discord Nitro的高级功能，包括高质量语音和自定义表情",
      app: "Discord",
      type: "免费试用",
      status: "即将开始",
      endDate: "2024-03-01",
      participants: 0,
      maxParticipants: 2000,
      image: "/api/placeholder/300/200",
      hot: false
    },
    {
      id: 4,
      title: "Spotify Premium 学生优惠",
      description: "学生专享Spotify Premium优惠价格，享受无广告音乐体验",
      app: "Spotify",
      type: "学生优惠",
      status: "进行中",
      endDate: "2024-03-15",
      participants: 2341,
      maxParticipants: 10000,
      image: "/api/placeholder/300/200",
      hot: false
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case "进行中": return "success"
      case "即将开始": return "warning"
      case "已结束": return "secondary"
      default: return "secondary"
    }
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="py-8">
        <Container>
          {/* 页面标题 */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-2">送码活动</h1>
            <p className="text-muted-foreground">
              参与活动，获取专属邀请码和会员福利
            </p>
          </div>

          {/* 活动统计 */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-primary mb-1">12</div>
                <div className="text-sm text-muted-foreground">进行中活动</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-success mb-1">4,567</div>
                <div className="text-sm text-muted-foreground">总参与人数</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-warning mb-1">89%</div>
                <div className="text-sm text-muted-foreground">成功率</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-info mb-1">156</div>
                <div className="text-sm text-muted-foreground">已发放码数</div>
              </CardContent>
            </Card>
          </div>

          {/* 筛选选项 */}
          <div className="flex flex-wrap gap-2 mb-8">
            <Button variant="default" size="sm">全部活动</Button>
            <Button variant="outline" size="sm">进行中</Button>
            <Button variant="outline" size="sm">即将开始</Button>
            <Button variant="outline" size="sm">会员福利</Button>
            <Button variant="outline" size="sm">邀请码</Button>
          </div>

          {/* 活动列表 */}
          <Grid cols={1} gap="md" className="md:grid-cols-2">
            {activities.map((activity) => (
              <Card key={activity.id} className="hover:shadow-lg transition-shadow">
                <div className="relative">
                  <img 
                    src={activity.image} 
                    alt={activity.title}
                    className="w-full h-48 object-cover rounded-t-lg"
                  />
                  {activity.hot && (
                    <Badge className="absolute top-3 left-3" variant="error">
                      🔥 热门
                    </Badge>
                  )}
                  <Badge 
                    className="absolute top-3 right-3" 
                    variant={getStatusColor(activity.status) as any}
                  >
                    {activity.status}
                  </Badge>
                </div>
                
                <CardHeader>
                  <div className="flex items-start justify-between gap-4">
                    <div className="flex-1">
                      <CardTitle className="text-lg mb-2">{activity.title}</CardTitle>
                      <p className="text-sm text-muted-foreground mb-3">
                        {activity.description}
                      </p>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Icons.app className="w-4 h-4" />
                          <span>{activity.app}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Icons.calendar className="w-4 h-4" />
                          <span>截止 {activity.endDate}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent className="pt-0">
                  <div className="mb-4">
                    <div className="flex justify-between text-sm mb-2">
                      <span>参与进度</span>
                      <span>{activity.participants}/{activity.maxParticipants}</span>
                    </div>
                    <div className="w-full bg-muted rounded-full h-2">
                      <div 
                        className="bg-primary h-2 rounded-full transition-all duration-300"
                        style={{ 
                          width: `${(activity.participants / activity.maxParticipants) * 100}%` 
                        }}
                      />
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button className="flex-1" size="sm">
                      <Icons.gift className="w-4 h-4 mr-2" />
                      立即参与
                    </Button>
                    <Button variant="outline" size="sm">
                      <Icons.share className="w-4 h-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </Grid>

          {/* 加载更多 */}
          <div className="text-center mt-8">
            <Button variant="outline">
              查看更多活动
            </Button>
          </div>
        </Container>
      </main>

      <Footer />
    </div>
  )
}
