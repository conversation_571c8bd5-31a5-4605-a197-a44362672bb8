"use client"

import * as React from "react"
import { Header } from "@/components/layout/header"
import { Footer } from "@/components/layout/footer"
import { <PERSON> } from "@/components/sections/hero"
import { Features } from "@/components/sections/features"
import { AppShowcase } from "@/components/sections/app-showcase"
import { Stats } from "@/components/sections/stats"
import { SearchModal } from "@/components/search/search-modal"
import { ToastProvider } from "@/components/ui/toast"

export default function Home() {
  const [isSearchOpen, setIsSearchOpen] = React.useState(false)

  // 全局快捷键
  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && e.key === "k") {
        e.preventDefault()
        setIsSearchOpen(true)
      }
    }

    document.addEventListener("keydown", handleKeyDown)
    return () => document.removeEventListener("keydown", handleKeyDown)
  }, [])

  return (
    <ToastProvider>
      <div className="min-h-screen bg-background">
        {/* 导航栏 */}
        <Header />

        {/* 主要内容 */}
        <main>
          {/* 英雄区 */}
          <Hero />

          {/* 特色功能 */}
          <Features />

          {/* 应用推荐 */}
          <AppShowcase />

          {/* 统计数据 */}
          <Stats />
        </main>

        {/* 页脚 */}
        <Footer />

        {/* 搜索模态框 */}
        <SearchModal
          isOpen={isSearchOpen}
          onClose={() => setIsSearchOpen(false)}
        />
      </div>
    </ToastProvider>
  )
}
