import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Container, Center } from "@/components/ui/layout"
import { Icons } from "@/components/ui/icon"

export default function NotFound() {
  return (
    <Container className="min-h-screen">
      <Center className="min-h-screen">
        <Card className="w-full max-w-md text-center">
          <CardHeader>
            <div className="w-16 h-16 bg-warning/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <Icons.search className="w-8 h-8 text-warning" />
            </div>
            <CardTitle className="text-xl">页面未找到</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-muted-foreground">
              抱歉，您访问的页面不存在或已被移动。
            </p>
            <div className="flex gap-3">
              <Button asChild className="flex-1">
                <Link href="/">
                  <Icons.home className="w-4 h-4 mr-2" />
                  返回首页
                </Link>
              </Button>
              <Button variant="outline" asChild className="flex-1">
                <Link href="/apps">
                  <Icons.app className="w-4 h-4 mr-2" />
                  浏览应用
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </Center>
    </Container>
  )
}
