import { Header } from "@/components/layout/header"
import { Footer } from "@/components/layout/footer"
import { Container, Grid, Stack } from "@/components/ui/layout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar"
import { Icons } from "@/components/ui/icon"
import { cn } from "@/utils"

export default function CommunityPage() {
  const posts = [
    {
      id: 1,
      title: "Notion使用技巧分享：如何构建个人知识管理系统",
      content: "最近在使用Notion构建个人知识管理系统，分享一些实用的模板和技巧...",
      author: {
        name: "张小明",
        avatar: "/api/placeholder/40/40",
        level: "活跃用户"
      },
      category: "经验分享",
      likes: 156,
      comments: 23,
      views: 1234,
      createdAt: "2小时前",
      tags: ["Notion", "知识管理", "效率"]
    },
    {
      id: 2,
      title: "Figma新功能体验：AI设计助手真的好用吗？",
      content: "最新的Figma AI功能已经上线，我第一时间体验了一下，来分享一下使用感受...",
      author: {
        name: "设计师小李",
        avatar: "/api/placeholder/40/40",
        level: "专业用户"
      },
      category: "产品评测",
      likes: 89,
      comments: 15,
      views: 567,
      createdAt: "5小时前",
      tags: ["Figma", "AI", "设计"]
    },
    {
      id: 3,
      title: "求助：Discord服务器管理有什么好的机器人推荐？",
      content: "最近在管理一个Discord服务器，想找一些好用的管理机器人，大家有什么推荐吗？",
      author: {
        name: "游戏玩家",
        avatar: "/api/placeholder/40/40",
        level: "新手用户"
      },
      category: "求助问答",
      likes: 12,
      comments: 8,
      views: 234,
      createdAt: "1天前",
      tags: ["Discord", "机器人", "管理"]
    }
  ]

  const categories = [
    { name: "经验分享", count: 156, color: "bg-blue-500" },
    { name: "产品评测", count: 89, color: "bg-green-500" },
    { name: "求助问答", count: 234, color: "bg-orange-500" },
    { name: "活动讨论", count: 67, color: "bg-purple-500" },
    { name: "新手指南", count: 123, color: "bg-pink-500" }
  ]

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="py-8">
        <Container>
          {/* 页面标题 */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-2">社区</h1>
            <p className="text-muted-foreground">
              与其他用户交流经验，分享使用心得
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* 主要内容区 */}
            <div className="lg:col-span-3">
              {/* 发布按钮和筛选 */}
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
                <div className="flex gap-2 flex-wrap">
                  <Button variant="default" size="sm">最新</Button>
                  <Button variant="outline" size="sm">热门</Button>
                  <Button variant="outline" size="sm">精华</Button>
                </div>
                <Button>
                  <Icons.plus className="w-4 h-4 mr-2" />
                  发布帖子
                </Button>
              </div>

              {/* 帖子列表 */}
              <Stack spacing="md">
                {posts.map((post) => (
                  <Card key={post.id} className="hover:shadow-md transition-shadow">
                    <CardHeader className="pb-4">
                      <div className="flex items-start gap-4">
                        <Avatar>
                          <AvatarImage src={post.author.avatar} alt={post.author.name} />
                          <AvatarFallback>
                            {post.author.name.slice(0, 2)}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="font-medium">{post.author.name}</span>
                            <Badge variant="secondary" size="sm">
                              {post.author.level}
                            </Badge>
                            <span className="text-sm text-muted-foreground">
                              {post.createdAt}
                            </span>
                          </div>
                          <Badge variant="outline" size="sm">
                            {post.category}
                          </Badge>
                        </div>
                      </div>
                    </CardHeader>
                    
                    <CardContent className="pt-0">
                      <h3 className="text-lg font-semibold mb-2 hover:text-primary cursor-pointer">
                        {post.title}
                      </h3>
                      <p className="text-muted-foreground mb-4 line-clamp-2">
                        {post.content}
                      </p>
                      
                      <div className="flex flex-wrap gap-1 mb-4">
                        {post.tags.map((tag) => (
                          <Badge key={tag} variant="secondary" size="sm">
                            #{tag}
                          </Badge>
                        ))}
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <Icons.heart className="w-4 h-4" />
                            <span>{post.likes}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Icons.message className="w-4 h-4" />
                            <span>{post.comments}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Icons.eye className="w-4 h-4" />
                            <span>{post.views}</span>
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Button variant="ghost" size="sm">
                            <Icons.heart className="w-4 h-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Icons.bookmark className="w-4 h-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Icons.share className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </Stack>

              {/* 加载更多 */}
              <div className="text-center mt-8">
                <Button variant="outline">
                  加载更多帖子
                </Button>
              </div>
            </div>

            {/* 侧边栏 */}
            <div className="space-y-6">
              {/* 社区统计 */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">社区统计</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">总帖子数</span>
                    <span className="font-semibold">1,234</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">活跃用户</span>
                    <span className="font-semibold">567</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">今日新增</span>
                    <span className="font-semibold">23</span>
                  </div>
                </CardContent>
              </Card>

              {/* 分类导航 */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">热门分类</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {categories.map((category) => (
                    <div key={category.name} className="flex items-center justify-between p-2 rounded-md hover:bg-muted cursor-pointer">
                      <div className="flex items-center gap-3">
                        <div className={cn("w-3 h-3 rounded-full", category.color)} />
                        <span className="text-sm">{category.name}</span>
                      </div>
                      <Badge variant="secondary" size="sm">
                        {category.count}
                      </Badge>
                    </div>
                  ))}
                </CardContent>
              </Card>

              {/* 社区规则 */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">社区规则</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2 text-sm text-muted-foreground">
                  <p>• 保持友善和尊重</p>
                  <p>• 分享有价值的内容</p>
                  <p>• 禁止发布广告信息</p>
                  <p>• 遵守相关法律法规</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </Container>
      </main>

      <Footer />
    </div>
  )
}
