{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/app/api/placeholder/%5B...params%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\n\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: { params: string[] } }\n) {\n  const [width = '64', height = '64'] = params.params || []\n  \n  // 解析尺寸\n  const w = parseInt(width) || 64\n  const h = parseInt(height) || 64\n  \n  // 限制尺寸范围\n  const finalWidth = Math.min(Math.max(w, 16), 512)\n  const finalHeight = Math.min(Math.max(h, 16), 512)\n  \n  // 生成SVG占位符\n  const svg = `\n    <svg width=\"${finalWidth}\" height=\"${finalHeight}\" xmlns=\"http://www.w3.org/2000/svg\">\n      <defs>\n        <linearGradient id=\"grad\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n          <stop offset=\"0%\" style=\"stop-color:#3b82f6;stop-opacity:1\" />\n          <stop offset=\"100%\" style=\"stop-color:#1d4ed8;stop-opacity:1\" />\n        </linearGradient>\n      </defs>\n      <rect width=\"100%\" height=\"100%\" fill=\"url(#grad)\" rx=\"8\"/>\n      <text x=\"50%\" y=\"50%\" text-anchor=\"middle\" dy=\"0.3em\" fill=\"white\" font-family=\"system-ui, sans-serif\" font-size=\"${Math.min(finalWidth, finalHeight) * 0.2}\" font-weight=\"600\">\n        APP\n      </text>\n    </svg>\n  `.trim()\n  \n  return new NextResponse(svg, {\n    headers: {\n      'Content-Type': 'image/svg+xml',\n      'Cache-Control': 'public, max-age=31536000, immutable',\n    },\n  })\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAoC;IAE5C,MAAM,CAAC,QAAQ,IAAI,EAAE,SAAS,IAAI,CAAC,GAAG,OAAO,MAAM,IAAI,EAAE;IAEzD,OAAO;IACP,MAAM,IAAI,SAAS,UAAU;IAC7B,MAAM,IAAI,SAAS,WAAW;IAE9B,SAAS;IACT,MAAM,aAAa,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG,KAAK;IAC7C,MAAM,cAAc,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG,KAAK;IAE9C,WAAW;IACX,MAAM,MAAM,CAAC;gBACC,EAAE,WAAW,UAAU,EAAE,YAAY;;;;;;;;wHAQmE,EAAE,KAAK,GAAG,CAAC,YAAY,eAAe,IAAI;;;;EAIhK,CAAC,CAAC,IAAI;IAEN,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,KAAK;QAC3B,SAAS;YACP,gBAAgB;YAChB,iBAAiB;QACnB;IACF;AACF", "debugId": null}}]}