{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/utils/index.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\n// 合并CSS类名\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// 格式化日期\nexport function formatDate(date: string | Date): string {\n  const d = new Date(date);\n  return d.toLocaleDateString('zh-CN', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  });\n}\n\n// 格式化相对时间\nexport function formatRelativeTime(date: string | Date): string {\n  const now = new Date();\n  const target = new Date(date);\n  const diffInSeconds = Math.floor((now.getTime() - target.getTime()) / 1000);\n\n  if (diffInSeconds < 60) {\n    return '刚刚';\n  } else if (diffInSeconds < 3600) {\n    const minutes = Math.floor(diffInSeconds / 60);\n    return `${minutes}分钟前`;\n  } else if (diffInSeconds < 86400) {\n    const hours = Math.floor(diffInSeconds / 3600);\n    return `${hours}小时前`;\n  } else if (diffInSeconds < 2592000) {\n    const days = Math.floor(diffInSeconds / 86400);\n    return `${days}天前`;\n  } else {\n    return formatDate(date);\n  }\n}\n\n// 生成随机字符串\nexport function generateRandomString(length: number): string {\n  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n  let result = '';\n  for (let i = 0; i < length; i++) {\n    result += chars.charAt(Math.floor(Math.random() * chars.length));\n  }\n  return result;\n}\n\n// 验证邮箱格式\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\n// 截断文本\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength) + '...';\n}\n\n// 应用分类映射\nexport const categoryMap: Record<string, string> = {\n  productivity: '效率工具',\n  entertainment: '娱乐',\n  education: '教育',\n  business: '商务',\n  lifestyle: '生活方式',\n  utilities: '实用工具',\n  games: '游戏',\n  social: '社交',\n  other: '其他'\n};\n\n// 获取分类显示名称\nexport function getCategoryDisplayName(category: string): string {\n  return categoryMap[category] || category;\n}\n\n// 验证URL格式\nexport function isValidUrl(url: string): boolean {\n  try {\n    new URL(url);\n    return true;\n  } catch {\n    return false;\n  }\n}\n\n// 计算活动热度分数\nexport function calculateActivityScore(\n  activityCount: number,\n  totalCodes: number,\n  createdAt: string\n): number {\n  const daysSinceCreation = Math.floor(\n    (Date.now() - new Date(createdAt).getTime()) / (1000 * 60 * 60 * 24)\n  );\n  \n  // 基础分数：活动数量 * 2 + 总邀请码数量\n  const baseScore = activityCount * 2 + totalCodes;\n  \n  // 时间衰减因子（越新的应用分数越高）\n  const timeDecay = Math.max(0.1, 1 - daysSinceCreation / 365);\n  \n  return Math.round(baseScore * timeDecay);\n}\n\n// 防抖函数\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n// 节流函数\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n}\n\n// 格式化数字为可读格式\nexport function formatNumber(num: number): string {\n  if (num >= 1000000) {\n    return (num / 1000000).toFixed(1) + 'M'\n  }\n  if (num >= 1000) {\n    return (num / 1000).toFixed(1) + 'K'\n  }\n  return num.toString()\n}\n\n// 深拷贝对象\nexport function deepClone<T>(obj: T): T {\n  if (obj === null || typeof obj !== 'object') return obj\n  if (obj instanceof Date) return new Date(obj.getTime()) as T\n  if (obj instanceof Array) return obj.map(item => deepClone(item)) as T\n  if (typeof obj === 'object') {\n    const clonedObj = {} as T\n    for (const key in obj) {\n      if (obj.hasOwnProperty(key)) {\n        clonedObj[key] = deepClone(obj[key])\n      }\n    }\n    return clonedObj\n  }\n  return obj\n}\n\n// 检查是否为移动设备\nexport function isMobile(): boolean {\n  if (typeof window === 'undefined') return false\n  return window.innerWidth < 768\n}\n\n// 平滑滚动到元素\nexport function scrollToElement(elementId: string, offset: number = 0): void {\n  const element = document.getElementById(elementId)\n  if (element) {\n    const top = element.offsetTop - offset\n    window.scrollTo({\n      top,\n      behavior: 'smooth'\n    })\n  }\n}\n\n// 复制文本到剪贴板\nexport async function copyToClipboard(text: string): Promise<boolean> {\n  try {\n    await navigator.clipboard.writeText(text)\n    return true\n  } catch (err) {\n    // 降级方案\n    const textArea = document.createElement('textarea')\n    textArea.value = text\n    document.body.appendChild(textArea)\n    textArea.select()\n    const success = document.execCommand('copy')\n    document.body.removeChild(textArea)\n    return success\n  }\n}\n\n// 获取文件大小的可读格式\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes'\n\n  const k = 1024\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n\n// 获取颜色的对比色\nexport function getContrastColor(hexColor: string): string {\n  // 移除 # 符号\n  const hex = hexColor.replace('#', '')\n\n  // 转换为 RGB\n  const r = parseInt(hex.substr(0, 2), 16)\n  const g = parseInt(hex.substr(2, 2), 16)\n  const b = parseInt(hex.substr(4, 2), 16)\n\n  // 计算亮度\n  const brightness = (r * 299 + g * 587 + b * 114) / 1000\n\n  // 返回对比色\n  return brightness > 128 ? '#000000' : '#ffffff'\n}\n\n// 生成随机ID\nexport function generateId(): string {\n  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)\n}\n\n// 格式化价格\nexport function formatPrice(price: number, currency: string = '¥'): string {\n  return `${currency}${price.toFixed(2)}`\n}\n\n// 获取设备类型\nexport function getDeviceType(): 'mobile' | 'tablet' | 'desktop' {\n  if (typeof window === 'undefined') return 'desktop'\n\n  const width = window.innerWidth\n  if (width < 768) return 'mobile'\n  if (width < 1024) return 'tablet'\n  return 'desktop'\n}\n\n// 检查是否支持WebP\nexport function supportsWebP(): Promise<boolean> {\n  return new Promise((resolve) => {\n    const webP = new Image()\n    webP.onload = webP.onerror = () => {\n      resolve(webP.height === 2)\n    }\n    webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA'\n  })\n}\n\n// 本地存储工具\nexport const storage = {\n  get: <T>(key: string, defaultValue?: T): T | null => {\n    if (typeof window === 'undefined') return defaultValue || null\n    try {\n      const item = localStorage.getItem(key)\n      return item ? JSON.parse(item) : defaultValue || null\n    } catch {\n      return defaultValue || null\n    }\n  },\n\n  set: <T>(key: string, value: T): void => {\n    if (typeof window === 'undefined') return\n    try {\n      localStorage.setItem(key, JSON.stringify(value))\n    } catch (error) {\n      console.error('Failed to save to localStorage:', error)\n    }\n  },\n\n  remove: (key: string): void => {\n    if (typeof window === 'undefined') return\n    localStorage.removeItem(key)\n  },\n\n  clear: (): void => {\n    if (typeof window === 'undefined') return\n    localStorage.clear()\n  }\n}\n\n// 生成头像的工具函数\nexport function generateAvatar(name: string, size: number = 64, colors?: string[]): string {\n  const defaultColors = [\n    '#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6',\n    '#06b6d4', '#84cc16', '#f97316', '#ec4899', '#6366f1'\n  ]\n\n  const colorPalette = colors || defaultColors\n  const initials = name\n    .split(' ')\n    .map(word => word[0])\n    .join('')\n    .toUpperCase()\n    .slice(0, 2)\n\n  // 根据名字生成一致的颜色\n  const colorIndex = name.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0) % colorPalette.length\n  const color = colorPalette[colorIndex]\n\n  const svg = `\n    <svg width=\"${size}\" height=\"${size}\" xmlns=\"http://www.w3.org/2000/svg\">\n      <defs>\n        <linearGradient id=\"grad-${name.replace(/\\s+/g, '')}\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n          <stop offset=\"0%\" style=\"stop-color:${color};stop-opacity:1\" />\n          <stop offset=\"100%\" style=\"stop-color:${color}dd;stop-opacity:1\" />\n        </linearGradient>\n      </defs>\n      <circle cx=\"${size/2}\" cy=\"${size/2}\" r=\"${size/2}\" fill=\"url(#grad-${name.replace(/\\s+/g, '')})\"/>\n      <text x=\"50%\" y=\"50%\" text-anchor=\"middle\" dy=\"0.3em\" fill=\"white\" font-family=\"system-ui, sans-serif\" font-size=\"${size * 0.3}\" font-weight=\"600\">\n        ${initials}\n      </text>\n    </svg>\n  `.trim()\n\n  return `data:image/svg+xml,${encodeURIComponent(svg)}`\n}\n\n// 生成应用图标的工具函数\nexport function generateAppIcon(name: string, color?: string, size: number = 64): string {\n  const defaultColor = '#3b82f6'\n  const iconColor = color || defaultColor\n  const initials = name\n    .split(' ')\n    .map(word => word[0])\n    .join('')\n    .toUpperCase()\n    .slice(0, 2)\n\n  const svg = `\n    <svg width=\"${size}\" height=\"${size}\" xmlns=\"http://www.w3.org/2000/svg\">\n      <defs>\n        <linearGradient id=\"app-grad-${name.replace(/\\s+/g, '')}\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n          <stop offset=\"0%\" style=\"stop-color:${iconColor};stop-opacity:1\" />\n          <stop offset=\"100%\" style=\"stop-color:${iconColor}dd;stop-opacity:1\" />\n        </linearGradient>\n      </defs>\n      <rect width=\"100%\" height=\"100%\" fill=\"url(#app-grad-${name.replace(/\\s+/g, '')})\" rx=\"${size * 0.2}\"/>\n      <text x=\"50%\" y=\"50%\" text-anchor=\"middle\" dy=\"0.3em\" fill=\"white\" font-family=\"system-ui, sans-serif\" font-size=\"${size * 0.3}\" font-weight=\"600\">\n        ${initials}\n      </text>\n    </svg>\n  `.trim()\n\n  return `data:image/svg+xml,${encodeURIComponent(svg)}`\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAGO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAGO,SAAS,mBAAmB,IAAmB;IACpD,MAAM,MAAM,IAAI;IAChB,MAAM,SAAS,IAAI,KAAK;IACxB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,OAAO,OAAO,EAAE,IAAI;IAEtE,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT,OAAO,IAAI,gBAAgB,MAAM;QAC/B,MAAM,UAAU,KAAK,KAAK,CAAC,gBAAgB;QAC3C,OAAO,GAAG,QAAQ,GAAG,CAAC;IACxB,OAAO,IAAI,gBAAgB,OAAO;QAChC,MAAM,QAAQ,KAAK,KAAK,CAAC,gBAAgB;QACzC,OAAO,GAAG,MAAM,GAAG,CAAC;IACtB,OAAO,IAAI,gBAAgB,SAAS;QAClC,MAAM,OAAO,KAAK,KAAK,CAAC,gBAAgB;QACxC,OAAO,GAAG,KAAK,EAAE,CAAC;IACpB,OAAO;QACL,OAAO,WAAW;IACpB;AACF;AAGO,SAAS,qBAAqB,MAAc;IACjD,MAAM,QAAQ;IACd,IAAI,SAAS;IACb,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,UAAU,MAAM,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM;IAChE;IACA,OAAO;AACT;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAGO,MAAM,cAAsC;IACjD,cAAc;IACd,eAAe;IACf,WAAW;IACX,UAAU;IACV,WAAW;IACX,WAAW;IACX,OAAO;IACP,QAAQ;IACR,OAAO;AACT;AAGO,SAAS,uBAAuB,QAAgB;IACrD,OAAO,WAAW,CAAC,SAAS,IAAI;AAClC;AAGO,SAAS,WAAW,GAAW;IACpC,IAAI;QACF,IAAI,IAAI;QACR,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAGO,SAAS,uBACd,aAAqB,EACrB,UAAkB,EAClB,SAAiB;IAEjB,MAAM,oBAAoB,KAAK,KAAK,CAClC,CAAC,KAAK,GAAG,KAAK,IAAI,KAAK,WAAW,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;IAGrE,yBAAyB;IACzB,MAAM,YAAY,gBAAgB,IAAI;IAEtC,oBAAoB;IACpB,MAAM,YAAY,KAAK,GAAG,CAAC,KAAK,IAAI,oBAAoB;IAExD,OAAO,KAAK,KAAK,CAAC,YAAY;AAChC;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF;AAGO,SAAS,aAAa,GAAW;IACtC,IAAI,OAAO,SAAS;QAClB,OAAO,CAAC,MAAM,OAAO,EAAE,OAAO,CAAC,KAAK;IACtC;IACA,IAAI,OAAO,MAAM;QACf,OAAO,CAAC,MAAM,IAAI,EAAE,OAAO,CAAC,KAAK;IACnC;IACA,OAAO,IAAI,QAAQ;AACrB;AAGO,SAAS,UAAa,GAAM;IACjC,IAAI,QAAQ,QAAQ,OAAO,QAAQ,UAAU,OAAO;IACpD,IAAI,eAAe,MAAM,OAAO,IAAI,KAAK,IAAI,OAAO;IACpD,IAAI,eAAe,OAAO,OAAO,IAAI,GAAG,CAAC,CAAA,OAAQ,UAAU;IAC3D,IAAI,OAAO,QAAQ,UAAU;QAC3B,MAAM,YAAY,CAAC;QACnB,IAAK,MAAM,OAAO,IAAK;YACrB,IAAI,IAAI,cAAc,CAAC,MAAM;gBAC3B,SAAS,CAAC,IAAI,GAAG,UAAU,GAAG,CAAC,IAAI;YACrC;QACF;QACA,OAAO;IACT;IACA,OAAO;AACT;AAGO,SAAS;IACd,wCAAmC,OAAO;;AAE5C;AAGO,SAAS,gBAAgB,SAAiB,EAAE,SAAiB,CAAC;IACnE,MAAM,UAAU,SAAS,cAAc,CAAC;IACxC,IAAI,SAAS;QACX,MAAM,MAAM,QAAQ,SAAS,GAAG;QAChC,OAAO,QAAQ,CAAC;YACd;YACA,UAAU;QACZ;IACF;AACF;AAGO,eAAe,gBAAgB,IAAY;IAChD,IAAI;QACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;QACpC,OAAO;IACT,EAAE,OAAO,KAAK;QACZ,OAAO;QACP,MAAM,WAAW,SAAS,aAAa,CAAC;QACxC,SAAS,KAAK,GAAG;QACjB,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,SAAS,MAAM;QACf,MAAM,UAAU,SAAS,WAAW,CAAC;QACrC,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,OAAO;IACT;AACF;AAGO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;KAAK;IAC/C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAGO,SAAS,iBAAiB,QAAgB;IAC/C,UAAU;IACV,MAAM,MAAM,SAAS,OAAO,CAAC,KAAK;IAElC,UAAU;IACV,MAAM,IAAI,SAAS,IAAI,MAAM,CAAC,GAAG,IAAI;IACrC,MAAM,IAAI,SAAS,IAAI,MAAM,CAAC,GAAG,IAAI;IACrC,MAAM,IAAI,SAAS,IAAI,MAAM,CAAC,GAAG,IAAI;IAErC,OAAO;IACP,MAAM,aAAa,CAAC,IAAI,MAAM,IAAI,MAAM,IAAI,GAAG,IAAI;IAEnD,QAAQ;IACR,OAAO,aAAa,MAAM,YAAY;AACxC;AAGO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;AAC/F;AAGO,SAAS,YAAY,KAAa,EAAE,WAAmB,GAAG;IAC/D,OAAO,GAAG,WAAW,MAAM,OAAO,CAAC,IAAI;AACzC;AAGO,SAAS;IACd,wCAAmC,OAAO;;IAE1C,MAAM;AAIR;AAGO,SAAS;IACd,OAAO,IAAI,QAAQ,CAAC;QAClB,MAAM,OAAO,IAAI;QACjB,KAAK,MAAM,GAAG,KAAK,OAAO,GAAG;YAC3B,QAAQ,KAAK,MAAM,KAAK;QAC1B;QACA,KAAK,GAAG,GAAG;IACb;AACF;AAGO,MAAM,UAAU;IACrB,KAAK,CAAI,KAAa;QACpB,wCAAmC,OAAO,gBAAgB;;IAO5D;IAEA,KAAK,CAAI,KAAa;QACpB,wCAAmC;;IAMrC;IAEA,QAAQ,CAAC;QACP,wCAAmC;;IAErC;IAEA,OAAO;QACL,wCAAmC;;IAErC;AACF;AAGO,SAAS,eAAe,IAAY,EAAE,OAAe,EAAE,EAAE,MAAiB;IAC/E,MAAM,gBAAgB;QACpB;QAAW;QAAW;QAAW;QAAW;QAC5C;QAAW;QAAW;QAAW;QAAW;KAC7C;IAED,MAAM,eAAe,UAAU;IAC/B,MAAM,WAAW,KACd,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,EAAE,EACnB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IAEZ,cAAc;IACd,MAAM,aAAa,KAAK,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,UAAU,CAAC,IAAI,KAAK,aAAa,MAAM;IAC1G,MAAM,QAAQ,YAAY,CAAC,WAAW;IAEtC,MAAM,MAAM,CAAC;gBACC,EAAE,KAAK,UAAU,EAAE,KAAK;;iCAEP,EAAE,KAAK,OAAO,CAAC,QAAQ,IAAI;8CACd,EAAE,MAAM;gDACN,EAAE,MAAM;;;kBAGtC,EAAE,OAAK,EAAE,MAAM,EAAE,OAAK,EAAE,KAAK,EAAE,OAAK,EAAE,kBAAkB,EAAE,KAAK,OAAO,CAAC,QAAQ,IAAI;wHACmB,EAAE,OAAO,IAAI;QAC7H,EAAE,SAAS;;;EAGjB,CAAC,CAAC,IAAI;IAEN,OAAO,CAAC,mBAAmB,EAAE,mBAAmB,MAAM;AACxD;AAGO,SAAS,gBAAgB,IAAY,EAAE,KAAc,EAAE,OAAe,EAAE;IAC7E,MAAM,eAAe;IACrB,MAAM,YAAY,SAAS;IAC3B,MAAM,WAAW,KACd,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,EAAE,EACnB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IAEZ,MAAM,MAAM,CAAC;gBACC,EAAE,KAAK,UAAU,EAAE,KAAK;;qCAEH,EAAE,KAAK,OAAO,CAAC,QAAQ,IAAI;8CAClB,EAAE,UAAU;gDACV,EAAE,UAAU;;;2DAGD,EAAE,KAAK,OAAO,CAAC,QAAQ,IAAI,OAAO,EAAE,OAAO,IAAI;wHACc,EAAE,OAAO,IAAI;QAC7H,EAAE,SAAS;;;EAGjB,CAAC,CAAC,IAAI;IAEN,OAAO,CAAC,mBAAmB,EAAE,mBAAmB,MAAM;AACxD", "debugId": null}}, {"offset": {"line": 316, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90 hover:shadow-md active:scale-95\",\n        destructive:\n          \"bg-error text-error-foreground shadow-sm hover:bg-error/90 hover:shadow-md active:scale-95\",\n        outline:\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground hover:shadow-md active:scale-95\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80 hover:shadow-md active:scale-95\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground active:scale-95\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n        success: \"bg-success text-success-foreground shadow hover:bg-success/90 hover:shadow-md active:scale-95\",\n        warning: \"bg-warning text-warning-foreground shadow hover:bg-warning/90 hover:shadow-md active:scale-95\",\n        info: \"bg-info text-info-foreground shadow hover:bg-info/90 hover:shadow-md active:scale-95\",\n        gradient: \"bg-gradient-to-r from-primary to-primary/80 text-primary-foreground shadow-lg hover:shadow-xl hover:scale-105 active:scale-95\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-md px-3 text-xs\",\n        lg: \"h-10 rounded-md px-8\",\n        xl: \"h-12 rounded-lg px-10 text-base\",\n        icon: \"h-9 w-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n  loading?: boolean\n  leftIcon?: React.ReactNode\n  rightIcon?: React.ReactNode\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({\n    className,\n    variant,\n    size,\n    asChild = false,\n    loading = false,\n    disabled,\n    children,\n    leftIcon,\n    rightIcon,\n    ...props\n  }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {leftIcon && !loading && leftIcon}\n        {children}\n        {rightIcon && !loading && rightIcon}\n      </Comp>\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+UACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;YACN,SAAS;YACT,SAAS;YACT,MAAM;YACN,UAAU;QACZ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAYF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EACC,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,UAAU,KAAK,EACf,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,GAAG,OACJ,EAAE;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,8OAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP,YAAY,CAAC,WAAW;YACxB;YACA,aAAa,CAAC,WAAW;;;;;;;AAGhC;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 420, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/utils\"\n\nconst cardVariants = cva(\n  \"rounded-lg border bg-card text-card-foreground shadow-sm transition-all duration-200\",\n  {\n    variants: {\n      variant: {\n        default: \"border-border\",\n        elevated: \"shadow-md hover:shadow-lg\",\n        interactive: \"cursor-pointer hover:shadow-md hover:scale-[1.02] active:scale-[0.98]\",\n        outline: \"border-2 border-primary/20 hover:border-primary/40\",\n        ghost: \"border-transparent shadow-none hover:bg-muted/50\",\n      },\n      padding: {\n        none: \"p-0\",\n        sm: \"p-3\",\n        default: \"p-6\",\n        lg: \"p-8\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      padding: \"default\",\n    },\n  }\n)\n\nexport interface CardProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof cardVariants> {}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, variant, padding, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(cardVariants({ variant, padding, className }))}\n      {...props}\n    />\n  )\n)\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,eAAe,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACrB,wFACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,UAAU;YACV,aAAa;YACb,SAAS;YACT,OAAO;QACT;QACA,SAAS;YACP,MAAM;YACN,IAAI;YACJ,SAAS;YACT,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,SAAS;IACX;AACF;AAOF,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC1B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC1C,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAAE;YAAS;YAAS;QAAU;QACxD,GAAG,KAAK;;;;;;AAIf,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 528, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/components/ui/layout.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/utils\"\n\n// 容器组件\nconst containerVariants = cva(\n  \"mx-auto w-full\",\n  {\n    variants: {\n      size: {\n        sm: \"max-w-screen-sm\",\n        md: \"max-w-screen-md\",\n        lg: \"max-w-screen-lg\",\n        xl: \"max-w-screen-xl\",\n        \"2xl\": \"max-w-screen-2xl\",\n        full: \"max-w-full\",\n      },\n      padding: {\n        none: \"px-0\",\n        sm: \"px-4\",\n        md: \"px-6\",\n        lg: \"px-8\",\n      },\n    },\n    defaultVariants: {\n      size: \"xl\",\n      padding: \"md\",\n    },\n  }\n)\n\nexport interface ContainerProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof containerVariants> {}\n\nconst Container = React.forwardRef<HTMLDivElement, ContainerProps>(\n  ({ className, size, padding, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(containerVariants({ size, padding, className }))}\n      {...props}\n    />\n  )\n)\nContainer.displayName = \"Container\"\n\n// 网格系统\nconst gridVariants = cva(\n  \"grid\",\n  {\n    variants: {\n      cols: {\n        1: \"grid-cols-1\",\n        2: \"grid-cols-1 md:grid-cols-2\",\n        3: \"grid-cols-1 md:grid-cols-2 lg:grid-cols-3\",\n        4: \"grid-cols-1 md:grid-cols-2 lg:grid-cols-4\",\n        5: \"grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5\",\n        6: \"grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6\",\n        12: \"grid-cols-12\",\n      },\n      gap: {\n        none: \"gap-0\",\n        sm: \"gap-2\",\n        md: \"gap-4\",\n        lg: \"gap-6\",\n        xl: \"gap-8\",\n      },\n    },\n    defaultVariants: {\n      cols: 1,\n      gap: \"md\",\n    },\n  }\n)\n\nexport interface GridProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof gridVariants> {}\n\nconst Grid = React.forwardRef<HTMLDivElement, GridProps>(\n  ({ className, cols, gap, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(gridVariants({ cols, gap, className }))}\n      {...props}\n    />\n  )\n)\nGrid.displayName = \"Grid\"\n\n// Flex布局组件\nconst flexVariants = cva(\n  \"flex\",\n  {\n    variants: {\n      direction: {\n        row: \"flex-row\",\n        col: \"flex-col\",\n        \"row-reverse\": \"flex-row-reverse\",\n        \"col-reverse\": \"flex-col-reverse\",\n      },\n      align: {\n        start: \"items-start\",\n        center: \"items-center\",\n        end: \"items-end\",\n        stretch: \"items-stretch\",\n        baseline: \"items-baseline\",\n      },\n      justify: {\n        start: \"justify-start\",\n        center: \"justify-center\",\n        end: \"justify-end\",\n        between: \"justify-between\",\n        around: \"justify-around\",\n        evenly: \"justify-evenly\",\n      },\n      wrap: {\n        nowrap: \"flex-nowrap\",\n        wrap: \"flex-wrap\",\n        \"wrap-reverse\": \"flex-wrap-reverse\",\n      },\n      gap: {\n        none: \"gap-0\",\n        sm: \"gap-2\",\n        md: \"gap-4\",\n        lg: \"gap-6\",\n        xl: \"gap-8\",\n      },\n    },\n    defaultVariants: {\n      direction: \"row\",\n      align: \"center\",\n      justify: \"start\",\n      wrap: \"nowrap\",\n      gap: \"none\",\n    },\n  }\n)\n\nexport interface FlexProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof flexVariants> {}\n\nconst Flex = React.forwardRef<HTMLDivElement, FlexProps>(\n  ({ className, direction, align, justify, wrap, gap, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(flexVariants({ direction, align, justify, wrap, gap, className }))}\n      {...props}\n    />\n  )\n)\nFlex.displayName = \"Flex\"\n\n// 堆栈组件\nconst stackVariants = cva(\n  \"flex\",\n  {\n    variants: {\n      direction: {\n        vertical: \"flex-col\",\n        horizontal: \"flex-row\",\n      },\n      spacing: {\n        none: \"gap-0\",\n        xs: \"gap-1\",\n        sm: \"gap-2\",\n        md: \"gap-4\",\n        lg: \"gap-6\",\n        xl: \"gap-8\",\n      },\n      align: {\n        start: \"items-start\",\n        center: \"items-center\",\n        end: \"items-end\",\n        stretch: \"items-stretch\",\n      },\n    },\n    defaultVariants: {\n      direction: \"vertical\",\n      spacing: \"md\",\n      align: \"stretch\",\n    },\n  }\n)\n\nexport interface StackProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof stackVariants> {}\n\nconst Stack = React.forwardRef<HTMLDivElement, StackProps>(\n  ({ className, direction, spacing, align, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(stackVariants({ direction, spacing, align, className }))}\n      {...props}\n    />\n  )\n)\nStack.displayName = \"Stack\"\n\n// 分隔符组件\nconst Separator = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & {\n    orientation?: \"horizontal\" | \"vertical\"\n  }\n>(({ className, orientation = \"horizontal\", ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"shrink-0 bg-border\",\n      orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\",\n      className\n    )}\n    {...props}\n  />\n))\nSeparator.displayName = \"Separator\"\n\n// 间距组件\nconst Spacer: React.FC<{ size?: \"sm\" | \"md\" | \"lg\" | \"xl\" }> = ({ size = \"md\" }) => {\n  const sizeClasses = {\n    sm: \"h-2\",\n    md: \"h-4\",\n    lg: \"h-6\",\n    xl: \"h-8\",\n  }\n  \n  return <div className={sizeClasses[size]} />\n}\n\n// 居中组件\nconst Center = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center justify-center\", className)}\n    {...props}\n  />\n))\nCenter.displayName = \"Center\"\n\n// 响应式显示组件\nconst Show: React.FC<{\n  when: boolean\n  fallback?: React.ReactNode\n  children: React.ReactNode\n}> = ({ when, fallback = null, children }) => {\n  return when ? <>{children}</> : <>{fallback}</>\n}\n\n// 断点显示组件\nconst Breakpoint: React.FC<{\n  show?: (\"sm\" | \"md\" | \"lg\" | \"xl\" | \"2xl\")[]\n  hide?: (\"sm\" | \"md\" | \"lg\" | \"xl\" | \"2xl\")[]\n  children: React.ReactNode\n}> = ({ show, hide, children }) => {\n  const showClasses = show?.map(bp => `${bp}:block`).join(\" \") || \"\"\n  const hideClasses = hide?.map(bp => `${bp}:hidden`).join(\" \") || \"\"\n  \n  return (\n    <div className={cn(\"hidden\", showClasses, hideClasses)}>\n      {children}\n    </div>\n  )\n}\n\nexport {\n  Container,\n  Grid,\n  Flex,\n  Stack,\n  Separator,\n  Spacer,\n  Center,\n  Show,\n  Breakpoint,\n  containerVariants,\n  gridVariants,\n  flexVariants,\n  stackVariants,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AACA;;;;;AAEA,OAAO;AACP,MAAM,oBAAoB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EAC1B,kBACA;IACE,UAAU;QACR,MAAM;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,OAAO;YACP,MAAM;QACR;QACA,SAAS;YACP,MAAM;YACN,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,MAAM;QACN,SAAS;IACX;AACF;AAOF,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC/B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBACvC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;YAAE;YAAM;YAAS;QAAU;QAC1D,GAAG,KAAK;;;;;;AAIf,UAAU,WAAW,GAAG;AAExB,OAAO;AACP,MAAM,eAAe,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACrB,QACA;IACE,UAAU;QACR,MAAM;YACJ,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG;YACH,IAAI;QACN;QACA,KAAK;YACH,MAAM;YACN,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,MAAM;QACN,KAAK;IACP;AACF;AAOF,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC1B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,OAAO,EAAE,oBACnC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAAE;YAAM;YAAK;QAAU;QACjD,GAAG,KAAK;;;;;;AAIf,KAAK,WAAW,GAAG;AAEnB,WAAW;AACX,MAAM,eAAe,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACrB,QACA;IACE,UAAU;QACR,WAAW;YACT,KAAK;YACL,KAAK;YACL,eAAe;YACf,eAAe;QACjB;QACA,OAAO;YACL,OAAO;YACP,QAAQ;YACR,KAAK;YACL,SAAS;YACT,UAAU;QACZ;QACA,SAAS;YACP,OAAO;YACP,QAAQ;YACR,KAAK;YACL,SAAS;YACT,QAAQ;YACR,QAAQ;QACV;QACA,MAAM;YACJ,QAAQ;YACR,MAAM;YACN,gBAAgB;QAClB;QACA,KAAK;YACH,MAAM;YACN,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,WAAW;QACX,OAAO;QACP,SAAS;QACT,MAAM;QACN,KAAK;IACP;AACF;AAOF,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC1B,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,OAAO,EAAE,oBAC9D,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAAE;YAAW;YAAO;YAAS;YAAM;YAAK;QAAU;QAC5E,GAAG,KAAK;;;;;;AAIf,KAAK,WAAW,GAAG;AAEnB,OAAO;AACP,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,QACA;IACE,UAAU;QACR,WAAW;YACT,UAAU;YACV,YAAY;QACd;QACA,SAAS;YACP,MAAM;YACN,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,OAAO;YACL,OAAO;YACP,QAAQ;YACR,KAAK;YACL,SAAS;QACX;IACF;IACA,iBAAiB;QACf,WAAW;QACX,SAAS;QACT,OAAO;IACT;AACF;AAOF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACnD,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;YAAW;YAAS;YAAO;QAAU;QAClE,GAAG,KAAK;;;;;;AAIf,MAAM,WAAW,GAAG;AAEpB,QAAQ;AACR,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAK/B,CAAC,EAAE,SAAS,EAAE,cAAc,YAAY,EAAE,GAAG,OAAO,EAAE,oBACtD,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACV,sBACA,gBAAgB,eAAe,mBAAmB,kBAClD;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,OAAO;AACP,MAAM,SAAyD,CAAC,EAAE,OAAO,IAAI,EAAE;IAC7E,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBAAO,8OAAC;QAAI,WAAW,WAAW,CAAC,KAAK;;;;;;AAC1C;AAEA,OAAO;AACP,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QACjD,GAAG,KAAK;;;;;;AAGb,OAAO,WAAW,GAAG;AAErB,UAAU;AACV,MAAM,OAID,CAAC,EAAE,IAAI,EAAE,WAAW,IAAI,EAAE,QAAQ,EAAE;IACvC,OAAO,qBAAO;kBAAG;sCAAe;kBAAG;;AACrC;AAEA,SAAS;AACT,MAAM,aAID,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE;IAC5B,MAAM,cAAc,MAAM,IAAI,CAAA,KAAM,GAAG,GAAG,MAAM,CAAC,EAAE,KAAK,QAAQ;IAChE,MAAM,cAAc,MAAM,IAAI,CAAA,KAAM,GAAG,GAAG,OAAO,CAAC,EAAE,KAAK,QAAQ;IAEjE,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,UAAU,aAAa;kBACvC;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 798, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/components/ui/icon.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/utils\"\nimport {\n  // 导航图标\n  Home,\n  Search,\n  User,\n  Settings,\n  Menu,\n  X,\n  ChevronDown,\n  ChevronUp,\n  ChevronLeft,\n  ChevronRight,\n  ArrowLeft,\n  ArrowRight,\n  ArrowUpDown,\n  \n  // 功能图标\n  Download,\n  Upload,\n  Share,\n  Heart,\n  Star,\n  Bookmark,\n  Eye,\n  EyeOff,\n  Edit,\n  Trash2,\n  Copy,\n  Check,\n  Plus,\n  Minus,\n  \n  // 状态图标\n  CheckCircle,\n  AlertCircle,\n  Info,\n  AlertTriangle,\n  Clock,\n  Calendar,\n  MapPin,\n  Phone,\n  Mail,\n  Globe,\n  \n  // 媒体图标\n  Play,\n  Pause,\n  Volume2,\n  VolumeX,\n  Image,\n  Video,\n  Music,\n  File,\n  FileText,\n  \n  // 社交图标\n  MessageCircle,\n  Send,\n  ThumbsUp,\n  ThumbsDown,\n  Users,\n  UserPlus,\n  \n  // 商业图标\n  ShoppingCart,\n  CreditCard,\n  DollarSign,\n  TrendingUp,\n  TrendingDown,\n  BarChart3,\n  PieChart,\n  \n  // 系统图标\n  Loader2,\n  RefreshCw,\n  Power,\n  Wifi,\n  WifiOff,\n  Battery,\n  Signal,\n  Shield,\n  Bell,\n  Flag,\n  Gift,\n  Lock,\n  Key,\n  Lightbulb,\n  Camera,\n  Zap,\n  Crown,\n  Github,\n  Filter,\n\n  type LucideIcon,\n} from \"lucide-react\"\n\nconst iconVariants = cva(\n  \"inline-flex items-center justify-center\",\n  {\n    variants: {\n      size: {\n        xs: \"h-3 w-3\",\n        sm: \"h-4 w-4\",\n        default: \"h-5 w-5\",\n        lg: \"h-6 w-6\",\n        xl: \"h-8 w-8\",\n        \"2xl\": \"h-10 w-10\",\n      },\n      variant: {\n        default: \"text-current\",\n        primary: \"text-primary\",\n        secondary: \"text-secondary\",\n        muted: \"text-muted-foreground\",\n        success: \"text-success\",\n        warning: \"text-warning\",\n        error: \"text-error\",\n        info: \"text-info\",\n      },\n    },\n    defaultVariants: {\n      size: \"default\",\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface IconProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof iconVariants> {\n  icon: LucideIcon\n  spinning?: boolean\n}\n\nconst Icon = React.forwardRef<HTMLDivElement, IconProps>(\n  ({ className, size, variant, icon: IconComponent, spinning = false, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(iconVariants({ size, variant, className }), spinning && \"animate-spin\")}\n      {...props}\n    >\n      <IconComponent className=\"h-full w-full\" />\n    </div>\n  )\n)\nIcon.displayName = \"Icon\"\n\n// 预定义的图标组件\nexport const Icons = {\n  // 导航\n  home: Home,\n  search: Search,\n  user: User,\n  settings: Settings,\n  menu: Menu,\n  close: X,\n  chevronDown: ChevronDown,\n  chevronUp: ChevronUp,\n  chevronLeft: ChevronLeft,\n  chevronRight: ChevronRight,\n  arrowLeft: ArrowLeft,\n  arrowRight: ArrowRight,\n  arrowUpDown: ArrowUpDown,\n  \n  // 功能\n  download: Download,\n  upload: Upload,\n  share: Share,\n  heart: Heart,\n  star: Star,\n  bookmark: Bookmark,\n  eye: Eye,\n  eyeOff: EyeOff,\n  edit: Edit,\n  trash: Trash2,\n  copy: Copy,\n  check: Check,\n  plus: Plus,\n  minus: Minus,\n  \n  // 状态\n  checkCircle: CheckCircle,\n  alertCircle: AlertCircle,\n  info: Info,\n  warning: AlertTriangle,\n  clock: Clock,\n  calendar: Calendar,\n  mapPin: MapPin,\n  phone: Phone,\n  mail: Mail,\n  globe: Globe,\n  \n  // 媒体\n  play: Play,\n  pause: Pause,\n  volume: Volume2,\n  volumeOff: VolumeX,\n  image: Image,\n  video: Video,\n  music: Music,\n  file: File,\n  fileText: FileText,\n  \n  // 社交\n  message: MessageCircle,\n  send: Send,\n  thumbsUp: ThumbsUp,\n  thumbsDown: ThumbsDown,\n  users: Users,\n  userPlus: UserPlus,\n  \n  // 商业\n  cart: ShoppingCart,\n  creditCard: CreditCard,\n  dollar: DollarSign,\n  trendingUp: TrendingUp,\n  trendingDown: TrendingDown,\n  barChart: BarChart3,\n  pieChart: PieChart,\n  \n  // 系统\n  loading: Loader2,\n  refresh: RefreshCw,\n  power: Power,\n  wifi: Wifi,\n  wifiOff: WifiOff,\n  battery: Battery,\n  signal: Signal,\n  shield: Shield,\n  bell: Bell,\n  flag: Flag,\n  gift: Gift,\n  lock: Lock,\n  key: Key,\n  lightbulb: Lightbulb,\n  camera: Camera,\n  zap: Zap,\n  crown: Crown,\n  github: Github,\n  filter: Filter,\n\n  // 自定义图标\n  logo: ({ className, ...props }: React.SVGProps<SVGSVGElement>) => (\n    <svg\n      className={className}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      stroke=\"currentColor\"\n      strokeWidth=\"2\"\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      {...props}\n    >\n      <path d=\"M12 2L2 7l10 5 10-5-10-5z\" />\n      <path d=\"M2 17l10 5 10-5\" />\n      <path d=\"M2 12l10 5 10-5\" />\n    </svg>\n  ),\n  \n  // 应用相关图标\n  app: ({ className, ...props }: React.SVGProps<SVGSVGElement>) => (\n    <svg\n      className={className}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      stroke=\"currentColor\"\n      strokeWidth=\"2\"\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      {...props}\n    >\n      <rect x=\"3\" y=\"3\" width=\"7\" height=\"7\" />\n      <rect x=\"14\" y=\"3\" width=\"7\" height=\"7\" />\n      <rect x=\"14\" y=\"14\" width=\"7\" height=\"7\" />\n      <rect x=\"3\" y=\"14\" width=\"7\" height=\"7\" />\n    </svg>\n  ),\n  \n  // 邀请码图标\n  code: ({ className, ...props }: React.SVGProps<SVGSVGElement>) => (\n    <svg\n      className={className}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      stroke=\"currentColor\"\n      strokeWidth=\"2\"\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      {...props}\n    >\n      <polyline points=\"16,18 22,12 16,6\" />\n      <polyline points=\"8,6 2,12 8,18\" />\n    </svg>\n  ),\n  \n  // 活动图标\n  activity: ({ className, ...props }: React.SVGProps<SVGSVGElement>) => (\n    <svg\n      className={className}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      stroke=\"currentColor\"\n      strokeWidth=\"2\"\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      {...props}\n    >\n      <polyline points=\"22,12 18,12 15,21 9,3 6,12 2,12\" />\n    </svg>\n  ),\n}\n\nexport { Icon, iconVariants, type LucideIcon }\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;AAgGA,MAAM,eAAe,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACrB,2CACA;IACE,UAAU;QACR,MAAM;YACJ,IAAI;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,OAAO;QACT;QACA,SAAS;YACP,SAAS;YACT,SAAS;YACT,WAAW;YACX,OAAO;YACP,SAAS;YACT,SAAS;YACT,OAAO;YACP,MAAM;QACR;IACF;IACA,iBAAiB;QACf,MAAM;QACN,SAAS;IACX;AACF;AAUF,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC1B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,aAAa,EAAE,WAAW,KAAK,EAAE,GAAG,OAAO,EAAE,oBAC9E,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAAE;YAAM;YAAS;QAAU,IAAI,YAAY;QACrE,GAAG,KAAK;kBAET,cAAA,8OAAC;YAAc,WAAU;;;;;;;;;;;AAI/B,KAAK,WAAW,GAAG;AAGZ,MAAM,QAAQ;IACnB,KAAK;IACL,MAAM,mMAAA,CAAA,OAAI;IACV,QAAQ,sMAAA,CAAA,SAAM;IACd,MAAM,kMAAA,CAAA,OAAI;IACV,UAAU,0MAAA,CAAA,WAAQ;IAClB,MAAM,kMAAA,CAAA,OAAI;IACV,OAAO,4LAAA,CAAA,IAAC;IACR,aAAa,oNAAA,CAAA,cAAW;IACxB,WAAW,gNAAA,CAAA,YAAS;IACpB,aAAa,oNAAA,CAAA,cAAW;IACxB,cAAc,sNAAA,CAAA,eAAY;IAC1B,WAAW,gNAAA,CAAA,YAAS;IACpB,YAAY,kNAAA,CAAA,aAAU;IACtB,aAAa,wNAAA,CAAA,cAAW;IAExB,KAAK;IACL,UAAU,0MAAA,CAAA,WAAQ;IAClB,QAAQ,sMAAA,CAAA,SAAM;IACd,OAAO,oMAAA,CAAA,QAAK;IACZ,OAAO,oMAAA,CAAA,QAAK;IACZ,MAAM,kMAAA,CAAA,OAAI;IACV,UAAU,0MAAA,CAAA,WAAQ;IAClB,KAAK,gMAAA,CAAA,MAAG;IACR,QAAQ,0MAAA,CAAA,SAAM;IACd,MAAM,2MAAA,CAAA,OAAI;IACV,OAAO,0MAAA,CAAA,SAAM;IACb,MAAM,kMAAA,CAAA,OAAI;IACV,OAAO,oMAAA,CAAA,QAAK;IACZ,MAAM,kMAAA,CAAA,OAAI;IACV,OAAO,oMAAA,CAAA,QAAK;IAEZ,KAAK;IACL,aAAa,2NAAA,CAAA,cAAW;IACxB,aAAa,oNAAA,CAAA,cAAW;IACxB,MAAM,kMAAA,CAAA,OAAI;IACV,SAAS,wNAAA,CAAA,gBAAa;IACtB,OAAO,oMAAA,CAAA,QAAK;IACZ,UAAU,0MAAA,CAAA,WAAQ;IAClB,QAAQ,0MAAA,CAAA,SAAM;IACd,OAAO,oMAAA,CAAA,QAAK;IACZ,MAAM,kMAAA,CAAA,OAAI;IACV,OAAO,oMAAA,CAAA,QAAK;IAEZ,KAAK;IACL,MAAM,kMAAA,CAAA,OAAI;IACV,OAAO,oMAAA,CAAA,QAAK;IACZ,QAAQ,4MAAA,CAAA,UAAO;IACf,WAAW,4MAAA,CAAA,UAAO;IAClB,OAAO,oMAAA,CAAA,QAAK;IACZ,OAAO,oMAAA,CAAA,QAAK;IACZ,OAAO,oMAAA,CAAA,QAAK;IACZ,MAAM,kMAAA,CAAA,OAAI;IACV,UAAU,8MAAA,CAAA,WAAQ;IAElB,KAAK;IACL,SAAS,wNAAA,CAAA,gBAAa;IACtB,MAAM,kMAAA,CAAA,OAAI;IACV,UAAU,8MAAA,CAAA,WAAQ;IAClB,YAAY,kNAAA,CAAA,aAAU;IACtB,OAAO,oMAAA,CAAA,QAAK;IACZ,UAAU,8MAAA,CAAA,WAAQ;IAElB,KAAK;IACL,MAAM,sNAAA,CAAA,eAAY;IAClB,YAAY,kNAAA,CAAA,aAAU;IACtB,QAAQ,kNAAA,CAAA,aAAU;IAClB,YAAY,kNAAA,CAAA,aAAU;IACtB,cAAc,sNAAA,CAAA,eAAY;IAC1B,UAAU,kNAAA,CAAA,YAAS;IACnB,UAAU,8MAAA,CAAA,WAAQ;IAElB,KAAK;IACL,SAAS,iNAAA,CAAA,UAAO;IAChB,SAAS,gNAAA,CAAA,YAAS;IAClB,OAAO,oMAAA,CAAA,QAAK;IACZ,MAAM,kMAAA,CAAA,OAAI;IACV,SAAS,4MAAA,CAAA,UAAO;IAChB,SAAS,wMAAA,CAAA,UAAO;IAChB,QAAQ,sMAAA,CAAA,SAAM;IACd,QAAQ,sMAAA,CAAA,SAAM;IACd,MAAM,kMAAA,CAAA,OAAI;IACV,MAAM,kMAAA,CAAA,OAAI;IACV,MAAM,kMAAA,CAAA,OAAI;IACV,MAAM,kMAAA,CAAA,OAAI;IACV,KAAK,gMAAA,CAAA,MAAG;IACR,WAAW,4MAAA,CAAA,YAAS;IACpB,QAAQ,sMAAA,CAAA,SAAM;IACd,KAAK,gMAAA,CAAA,MAAG;IACR,OAAO,oMAAA,CAAA,QAAK;IACZ,QAAQ,sMAAA,CAAA,SAAM;IACd,QAAQ,sMAAA,CAAA,SAAM;IAEd,QAAQ;IACR,MAAM,CAAC,EAAE,SAAS,EAAE,GAAG,OAAsC,iBAC3D,8OAAC;YACC,WAAW;YACX,SAAQ;YACR,MAAK;YACL,QAAO;YACP,aAAY;YACZ,eAAc;YACd,gBAAe;YACd,GAAG,KAAK;;8BAET,8OAAC;oBAAK,GAAE;;;;;;8BACR,8OAAC;oBAAK,GAAE;;;;;;8BACR,8OAAC;oBAAK,GAAE;;;;;;;;;;;;IAIZ,SAAS;IACT,KAAK,CAAC,EAAE,SAAS,EAAE,GAAG,OAAsC,iBAC1D,8OAAC;YACC,WAAW;YACX,SAAQ;YACR,MAAK;YACL,QAAO;YACP,aAAY;YACZ,eAAc;YACd,gBAAe;YACd,GAAG,KAAK;;8BAET,8OAAC;oBAAK,GAAE;oBAAI,GAAE;oBAAI,OAAM;oBAAI,QAAO;;;;;;8BACnC,8OAAC;oBAAK,GAAE;oBAAK,GAAE;oBAAI,OAAM;oBAAI,QAAO;;;;;;8BACpC,8OAAC;oBAAK,GAAE;oBAAK,GAAE;oBAAK,OAAM;oBAAI,QAAO;;;;;;8BACrC,8OAAC;oBAAK,GAAE;oBAAI,GAAE;oBAAK,OAAM;oBAAI,QAAO;;;;;;;;;;;;IAIxC,QAAQ;IACR,MAAM,CAAC,EAAE,SAAS,EAAE,GAAG,OAAsC,iBAC3D,8OAAC;YACC,WAAW;YACX,SAAQ;YACR,MAAK;YACL,QAAO;YACP,aAAY;YACZ,eAAc;YACd,gBAAe;YACd,GAAG,KAAK;;8BAET,8OAAC;oBAAS,QAAO;;;;;;8BACjB,8OAAC;oBAAS,QAAO;;;;;;;;;;;;IAIrB,OAAO;IACP,UAAU,CAAC,EAAE,SAAS,EAAE,GAAG,OAAsC,iBAC/D,8OAAC;YACC,WAAW;YACX,SAAQ;YACR,MAAK;YACL,QAAO;YACP,aAAY;YACZ,eAAc;YACd,gBAAe;YACd,GAAG,KAAK;sBAET,cAAA,8OAAC;gBAAS,QAAO;;;;;;;;;;;AAGvB", "debugId": null}}, {"offset": {"line": 1179, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/app/error.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useEffect } from \"react\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Container, Center } from \"@/components/ui/layout\"\nimport { Icons } from \"@/components/ui/icon\"\n\nexport default function Error({\n  error,\n  reset,\n}: {\n  error: Error & { digest?: string }\n  reset: () => void\n}) {\n  useEffect(() => {\n    // 记录错误到错误报告服务\n    console.error(error)\n  }, [error])\n\n  return (\n    <Container className=\"min-h-screen\">\n      <Center className=\"min-h-screen\">\n        <Card className=\"w-full max-w-md text-center\">\n          <CardHeader>\n            <div className=\"w-16 h-16 bg-error/10 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <Icons.alertCircle className=\"w-8 h-8 text-error\" />\n            </div>\n            <CardTitle className=\"text-xl\">出现了一些问题</CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <p className=\"text-muted-foreground\">\n              很抱歉，页面加载时出现了错误。请稍后重试。\n            </p>\n            <div className=\"flex gap-3\">\n              <Button onClick={reset} className=\"flex-1\">\n                <Icons.refresh className=\"w-4 h-4 mr-2\" />\n                重试\n              </Button>\n              <Button variant=\"outline\" onClick={() => window.location.href = \"/\"} className=\"flex-1\">\n                <Icons.home className=\"w-4 h-4 mr-2\" />\n                返回首页\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n      </Center>\n    </Container>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQe,SAAS,MAAM,EAC5B,KAAK,EACL,KAAK,EAIN;IACC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc;QACd,QAAQ,KAAK,CAAC;IAChB,GAAG;QAAC;KAAM;IAEV,qBACE,8OAAC,kIAAA,CAAA,YAAS;QAAC,WAAU;kBACnB,cAAA,8OAAC,kIAAA,CAAA,SAAM;YAAC,WAAU;sBAChB,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,gIAAA,CAAA,QAAK,CAAC,WAAW;oCAAC,WAAU;;;;;;;;;;;0CAE/B,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAU;;;;;;;;;;;;kCAEjC,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;0CAGrC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAS;wCAAO,WAAU;;0DAChC,8OAAC,gIAAA,CAAA,QAAK,CAAC,OAAO;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAG5C,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;wCAAK,WAAU;;0DAC7E,8OAAC,gIAAA,CAAA,QAAK,CAAC,IAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASvD", "debugId": null}}]}