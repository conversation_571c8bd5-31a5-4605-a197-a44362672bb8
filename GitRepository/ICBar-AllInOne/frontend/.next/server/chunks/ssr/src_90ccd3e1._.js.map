{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90 hover:shadow-md active:scale-95\",\n        destructive:\n          \"bg-error text-error-foreground shadow-sm hover:bg-error/90 hover:shadow-md active:scale-95\",\n        outline:\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground hover:shadow-md active:scale-95\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80 hover:shadow-md active:scale-95\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground active:scale-95\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n        success: \"bg-success text-success-foreground shadow hover:bg-success/90 hover:shadow-md active:scale-95\",\n        warning: \"bg-warning text-warning-foreground shadow hover:bg-warning/90 hover:shadow-md active:scale-95\",\n        info: \"bg-info text-info-foreground shadow hover:bg-info/90 hover:shadow-md active:scale-95\",\n        gradient: \"bg-gradient-to-r from-primary to-primary/80 text-primary-foreground shadow-lg hover:shadow-xl hover:scale-105 active:scale-95\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-md px-3 text-xs\",\n        lg: \"h-10 rounded-md px-8\",\n        xl: \"h-12 rounded-lg px-10 text-base\",\n        icon: \"h-9 w-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n  loading?: boolean\n  leftIcon?: React.ReactNode\n  rightIcon?: React.ReactNode\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({\n    className,\n    variant,\n    size,\n    asChild = false,\n    loading = false,\n    disabled,\n    children,\n    leftIcon,\n    rightIcon,\n    ...props\n  }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {leftIcon && !loading && leftIcon}\n        {children}\n        {rightIcon && !loading && rightIcon}\n      </Comp>\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+UACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;YACN,SAAS;YACT,SAAS;YACT,MAAM;YACN,UAAU;QACZ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAYF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EACC,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,UAAU,KAAK,EACf,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,GAAG,OACJ,EAAE;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,8OAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP,YAAY,CAAC,WAAW;YACxB;YACA,aAAa,CAAC,WAAW;;;;;;;AAGhC;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/utils\"\n\nconst cardVariants = cva(\n  \"rounded-lg border bg-card text-card-foreground shadow-sm transition-all duration-200\",\n  {\n    variants: {\n      variant: {\n        default: \"border-border\",\n        elevated: \"shadow-md hover:shadow-lg\",\n        interactive: \"cursor-pointer hover:shadow-md hover:scale-[1.02] active:scale-[0.98]\",\n        outline: \"border-2 border-primary/20 hover:border-primary/40\",\n        ghost: \"border-transparent shadow-none hover:bg-muted/50\",\n      },\n      padding: {\n        none: \"p-0\",\n        sm: \"p-3\",\n        default: \"p-6\",\n        lg: \"p-8\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      padding: \"default\",\n    },\n  }\n)\n\nexport interface CardProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof cardVariants> {}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, variant, padding, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(cardVariants({ variant, padding, className }))}\n      {...props}\n    />\n  )\n)\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,eAAe,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACrB,wFACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,UAAU;YACV,aAAa;YACb,SAAS;YACT,OAAO;QACT;QACA,SAAS;YACP,MAAM;YACN,IAAI;YACJ,SAAS;YACT,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,SAAS;IACX;AACF;AAOF,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC1B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC1C,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAAE;YAAS;YAAS;QAAU;QACxD,GAAG,KAAK;;;;;;AAIf,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 219, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/components/ui/layout.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/utils\"\n\n// 容器组件\nconst containerVariants = cva(\n  \"mx-auto w-full\",\n  {\n    variants: {\n      size: {\n        sm: \"max-w-screen-sm\",\n        md: \"max-w-screen-md\",\n        lg: \"max-w-screen-lg\",\n        xl: \"max-w-screen-xl\",\n        \"2xl\": \"max-w-screen-2xl\",\n        full: \"max-w-full\",\n      },\n      padding: {\n        none: \"px-0\",\n        sm: \"px-4\",\n        md: \"px-6\",\n        lg: \"px-8\",\n      },\n    },\n    defaultVariants: {\n      size: \"xl\",\n      padding: \"md\",\n    },\n  }\n)\n\nexport interface ContainerProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof containerVariants> {}\n\nconst Container = React.forwardRef<HTMLDivElement, ContainerProps>(\n  ({ className, size, padding, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(containerVariants({ size, padding, className }))}\n      {...props}\n    />\n  )\n)\nContainer.displayName = \"Container\"\n\n// 网格系统\nconst gridVariants = cva(\n  \"grid\",\n  {\n    variants: {\n      cols: {\n        1: \"grid-cols-1\",\n        2: \"grid-cols-1 md:grid-cols-2\",\n        3: \"grid-cols-1 md:grid-cols-2 lg:grid-cols-3\",\n        4: \"grid-cols-1 md:grid-cols-2 lg:grid-cols-4\",\n        5: \"grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5\",\n        6: \"grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6\",\n        12: \"grid-cols-12\",\n      },\n      gap: {\n        none: \"gap-0\",\n        sm: \"gap-2\",\n        md: \"gap-4\",\n        lg: \"gap-6\",\n        xl: \"gap-8\",\n      },\n    },\n    defaultVariants: {\n      cols: 1,\n      gap: \"md\",\n    },\n  }\n)\n\nexport interface GridProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof gridVariants> {}\n\nconst Grid = React.forwardRef<HTMLDivElement, GridProps>(\n  ({ className, cols, gap, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(gridVariants({ cols, gap, className }))}\n      {...props}\n    />\n  )\n)\nGrid.displayName = \"Grid\"\n\n// Flex布局组件\nconst flexVariants = cva(\n  \"flex\",\n  {\n    variants: {\n      direction: {\n        row: \"flex-row\",\n        col: \"flex-col\",\n        \"row-reverse\": \"flex-row-reverse\",\n        \"col-reverse\": \"flex-col-reverse\",\n      },\n      align: {\n        start: \"items-start\",\n        center: \"items-center\",\n        end: \"items-end\",\n        stretch: \"items-stretch\",\n        baseline: \"items-baseline\",\n      },\n      justify: {\n        start: \"justify-start\",\n        center: \"justify-center\",\n        end: \"justify-end\",\n        between: \"justify-between\",\n        around: \"justify-around\",\n        evenly: \"justify-evenly\",\n      },\n      wrap: {\n        nowrap: \"flex-nowrap\",\n        wrap: \"flex-wrap\",\n        \"wrap-reverse\": \"flex-wrap-reverse\",\n      },\n      gap: {\n        none: \"gap-0\",\n        sm: \"gap-2\",\n        md: \"gap-4\",\n        lg: \"gap-6\",\n        xl: \"gap-8\",\n      },\n    },\n    defaultVariants: {\n      direction: \"row\",\n      align: \"center\",\n      justify: \"start\",\n      wrap: \"nowrap\",\n      gap: \"none\",\n    },\n  }\n)\n\nexport interface FlexProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof flexVariants> {}\n\nconst Flex = React.forwardRef<HTMLDivElement, FlexProps>(\n  ({ className, direction, align, justify, wrap, gap, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(flexVariants({ direction, align, justify, wrap, gap, className }))}\n      {...props}\n    />\n  )\n)\nFlex.displayName = \"Flex\"\n\n// 堆栈组件\nconst stackVariants = cva(\n  \"flex\",\n  {\n    variants: {\n      direction: {\n        vertical: \"flex-col\",\n        horizontal: \"flex-row\",\n      },\n      spacing: {\n        none: \"gap-0\",\n        xs: \"gap-1\",\n        sm: \"gap-2\",\n        md: \"gap-4\",\n        lg: \"gap-6\",\n        xl: \"gap-8\",\n      },\n      align: {\n        start: \"items-start\",\n        center: \"items-center\",\n        end: \"items-end\",\n        stretch: \"items-stretch\",\n      },\n    },\n    defaultVariants: {\n      direction: \"vertical\",\n      spacing: \"md\",\n      align: \"stretch\",\n    },\n  }\n)\n\nexport interface StackProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof stackVariants> {}\n\nconst Stack = React.forwardRef<HTMLDivElement, StackProps>(\n  ({ className, direction, spacing, align, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(stackVariants({ direction, spacing, align, className }))}\n      {...props}\n    />\n  )\n)\nStack.displayName = \"Stack\"\n\n// 分隔符组件\nconst Separator = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & {\n    orientation?: \"horizontal\" | \"vertical\"\n  }\n>(({ className, orientation = \"horizontal\", ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"shrink-0 bg-border\",\n      orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\",\n      className\n    )}\n    {...props}\n  />\n))\nSeparator.displayName = \"Separator\"\n\n// 间距组件\nconst Spacer: React.FC<{ size?: \"sm\" | \"md\" | \"lg\" | \"xl\" }> = ({ size = \"md\" }) => {\n  const sizeClasses = {\n    sm: \"h-2\",\n    md: \"h-4\",\n    lg: \"h-6\",\n    xl: \"h-8\",\n  }\n  \n  return <div className={sizeClasses[size]} />\n}\n\n// 居中组件\nconst Center = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center justify-center\", className)}\n    {...props}\n  />\n))\nCenter.displayName = \"Center\"\n\n// 响应式显示组件\nconst Show: React.FC<{\n  when: boolean\n  fallback?: React.ReactNode\n  children: React.ReactNode\n}> = ({ when, fallback = null, children }) => {\n  return when ? <>{children}</> : <>{fallback}</>\n}\n\n// 断点显示组件\nconst Breakpoint: React.FC<{\n  show?: (\"sm\" | \"md\" | \"lg\" | \"xl\" | \"2xl\")[]\n  hide?: (\"sm\" | \"md\" | \"lg\" | \"xl\" | \"2xl\")[]\n  children: React.ReactNode\n}> = ({ show, hide, children }) => {\n  const showClasses = show?.map(bp => `${bp}:block`).join(\" \") || \"\"\n  const hideClasses = hide?.map(bp => `${bp}:hidden`).join(\" \") || \"\"\n  \n  return (\n    <div className={cn(\"hidden\", showClasses, hideClasses)}>\n      {children}\n    </div>\n  )\n}\n\nexport {\n  Container,\n  Grid,\n  Flex,\n  Stack,\n  Separator,\n  Spacer,\n  Center,\n  Show,\n  Breakpoint,\n  containerVariants,\n  gridVariants,\n  flexVariants,\n  stackVariants,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AACA;;;;;AAEA,OAAO;AACP,MAAM,oBAAoB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EAC1B,kBACA;IACE,UAAU;QACR,MAAM;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,OAAO;YACP,MAAM;QACR;QACA,SAAS;YACP,MAAM;YACN,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,MAAM;QACN,SAAS;IACX;AACF;AAOF,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC/B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBACvC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;YAAE;YAAM;YAAS;QAAU;QAC1D,GAAG,KAAK;;;;;;AAIf,UAAU,WAAW,GAAG;AAExB,OAAO;AACP,MAAM,eAAe,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACrB,QACA;IACE,UAAU;QACR,MAAM;YACJ,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG;YACH,IAAI;QACN;QACA,KAAK;YACH,MAAM;YACN,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,MAAM;QACN,KAAK;IACP;AACF;AAOF,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC1B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,OAAO,EAAE,oBACnC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAAE;YAAM;YAAK;QAAU;QACjD,GAAG,KAAK;;;;;;AAIf,KAAK,WAAW,GAAG;AAEnB,WAAW;AACX,MAAM,eAAe,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACrB,QACA;IACE,UAAU;QACR,WAAW;YACT,KAAK;YACL,KAAK;YACL,eAAe;YACf,eAAe;QACjB;QACA,OAAO;YACL,OAAO;YACP,QAAQ;YACR,KAAK;YACL,SAAS;YACT,UAAU;QACZ;QACA,SAAS;YACP,OAAO;YACP,QAAQ;YACR,KAAK;YACL,SAAS;YACT,QAAQ;YACR,QAAQ;QACV;QACA,MAAM;YACJ,QAAQ;YACR,MAAM;YACN,gBAAgB;QAClB;QACA,KAAK;YACH,MAAM;YACN,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,WAAW;QACX,OAAO;QACP,SAAS;QACT,MAAM;QACN,KAAK;IACP;AACF;AAOF,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC1B,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,OAAO,EAAE,oBAC9D,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAAE;YAAW;YAAO;YAAS;YAAM;YAAK;QAAU;QAC5E,GAAG,KAAK;;;;;;AAIf,KAAK,WAAW,GAAG;AAEnB,OAAO;AACP,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,QACA;IACE,UAAU;QACR,WAAW;YACT,UAAU;YACV,YAAY;QACd;QACA,SAAS;YACP,MAAM;YACN,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,OAAO;YACL,OAAO;YACP,QAAQ;YACR,KAAK;YACL,SAAS;QACX;IACF;IACA,iBAAiB;QACf,WAAW;QACX,SAAS;QACT,OAAO;IACT;AACF;AAOF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACnD,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;YAAW;YAAS;YAAO;QAAU;QAClE,GAAG,KAAK;;;;;;AAIf,MAAM,WAAW,GAAG;AAEpB,QAAQ;AACR,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAK/B,CAAC,EAAE,SAAS,EAAE,cAAc,YAAY,EAAE,GAAG,OAAO,EAAE,oBACtD,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACV,sBACA,gBAAgB,eAAe,mBAAmB,kBAClD;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,OAAO;AACP,MAAM,SAAyD,CAAC,EAAE,OAAO,IAAI,EAAE;IAC7E,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBAAO,8OAAC;QAAI,WAAW,WAAW,CAAC,KAAK;;;;;;AAC1C;AAEA,OAAO;AACP,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QACjD,GAAG,KAAK;;;;;;AAGb,OAAO,WAAW,GAAG;AAErB,UAAU;AACV,MAAM,OAID,CAAC,EAAE,IAAI,EAAE,WAAW,IAAI,EAAE,QAAQ,EAAE;IACvC,OAAO,qBAAO;kBAAG;sCAAe;kBAAG;;AACrC;AAEA,SAAS;AACT,MAAM,aAID,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE;IAC5B,MAAM,cAAc,MAAM,IAAI,CAAA,KAAM,GAAG,GAAG,MAAM,CAAC,EAAE,KAAK,QAAQ;IAChE,MAAM,cAAc,MAAM,IAAI,CAAA,KAAM,GAAG,GAAG,OAAO,CAAC,EAAE,KAAK,QAAQ;IAEjE,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,UAAU,aAAa;kBACvC;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 489, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/components/ui/icon.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/utils\"\nimport {\n  // 导航图标\n  Home,\n  Search,\n  User,\n  Settings,\n  Menu,\n  X,\n  ChevronDown,\n  ChevronUp,\n  ChevronLeft,\n  ChevronRight,\n  ArrowLeft,\n  ArrowRight,\n  ArrowUpDown,\n  \n  // 功能图标\n  Download,\n  Upload,\n  Share,\n  Heart,\n  Star,\n  Bookmark,\n  Eye,\n  EyeOff,\n  Edit,\n  Trash2,\n  Copy,\n  Check,\n  Plus,\n  Minus,\n  \n  // 状态图标\n  CheckCircle,\n  AlertCircle,\n  Info,\n  AlertTriangle,\n  Clock,\n  Calendar,\n  MapPin,\n  Phone,\n  Mail,\n  Globe,\n  \n  // 媒体图标\n  Play,\n  Pause,\n  Volume2,\n  VolumeX,\n  Image,\n  Video,\n  Music,\n  File,\n  FileText,\n  \n  // 社交图标\n  MessageCircle,\n  Send,\n  ThumbsUp,\n  ThumbsDown,\n  Users,\n  UserPlus,\n  \n  // 商业图标\n  ShoppingCart,\n  CreditCard,\n  DollarSign,\n  TrendingUp,\n  TrendingDown,\n  BarChart3,\n  PieChart,\n  \n  // 系统图标\n  Loader2,\n  RefreshCw,\n  Power,\n  Wifi,\n  WifiOff,\n  Battery,\n  Signal,\n  Shield,\n  Bell,\n  Flag,\n  Gift,\n  Lock,\n  Key,\n  Lightbulb,\n  Camera,\n  Zap,\n  Crown,\n  Github,\n  Filter,\n\n  type LucideIcon,\n} from \"lucide-react\"\n\nconst iconVariants = cva(\n  \"inline-flex items-center justify-center\",\n  {\n    variants: {\n      size: {\n        xs: \"h-3 w-3\",\n        sm: \"h-4 w-4\",\n        default: \"h-5 w-5\",\n        lg: \"h-6 w-6\",\n        xl: \"h-8 w-8\",\n        \"2xl\": \"h-10 w-10\",\n      },\n      variant: {\n        default: \"text-current\",\n        primary: \"text-primary\",\n        secondary: \"text-secondary\",\n        muted: \"text-muted-foreground\",\n        success: \"text-success\",\n        warning: \"text-warning\",\n        error: \"text-error\",\n        info: \"text-info\",\n      },\n    },\n    defaultVariants: {\n      size: \"default\",\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface IconProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof iconVariants> {\n  icon: LucideIcon\n  spinning?: boolean\n}\n\nconst Icon = React.forwardRef<HTMLDivElement, IconProps>(\n  ({ className, size, variant, icon: IconComponent, spinning = false, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(iconVariants({ size, variant, className }), spinning && \"animate-spin\")}\n      {...props}\n    >\n      <IconComponent className=\"h-full w-full\" />\n    </div>\n  )\n)\nIcon.displayName = \"Icon\"\n\n// 预定义的图标组件\nexport const Icons = {\n  // 导航\n  home: Home,\n  search: Search,\n  user: User,\n  settings: Settings,\n  menu: Menu,\n  close: X,\n  chevronDown: ChevronDown,\n  chevronUp: ChevronUp,\n  chevronLeft: ChevronLeft,\n  chevronRight: ChevronRight,\n  arrowLeft: ArrowLeft,\n  arrowRight: ArrowRight,\n  arrowUpDown: ArrowUpDown,\n  \n  // 功能\n  download: Download,\n  upload: Upload,\n  share: Share,\n  heart: Heart,\n  star: Star,\n  bookmark: Bookmark,\n  eye: Eye,\n  eyeOff: EyeOff,\n  edit: Edit,\n  trash: Trash2,\n  copy: Copy,\n  check: Check,\n  plus: Plus,\n  minus: Minus,\n  \n  // 状态\n  checkCircle: CheckCircle,\n  alertCircle: AlertCircle,\n  info: Info,\n  warning: AlertTriangle,\n  clock: Clock,\n  calendar: Calendar,\n  mapPin: MapPin,\n  phone: Phone,\n  mail: Mail,\n  globe: Globe,\n  \n  // 媒体\n  play: Play,\n  pause: Pause,\n  volume: Volume2,\n  volumeOff: VolumeX,\n  image: Image,\n  video: Video,\n  music: Music,\n  file: File,\n  fileText: FileText,\n  \n  // 社交\n  message: MessageCircle,\n  send: Send,\n  thumbsUp: ThumbsUp,\n  thumbsDown: ThumbsDown,\n  users: Users,\n  userPlus: UserPlus,\n  \n  // 商业\n  cart: ShoppingCart,\n  creditCard: CreditCard,\n  dollar: DollarSign,\n  trendingUp: TrendingUp,\n  trendingDown: TrendingDown,\n  barChart: BarChart3,\n  pieChart: PieChart,\n  \n  // 系统\n  loading: Loader2,\n  refresh: RefreshCw,\n  power: Power,\n  wifi: Wifi,\n  wifiOff: WifiOff,\n  battery: Battery,\n  signal: Signal,\n  shield: Shield,\n  bell: Bell,\n  flag: Flag,\n  gift: Gift,\n  lock: Lock,\n  key: Key,\n  lightbulb: Lightbulb,\n  camera: Camera,\n  zap: Zap,\n  crown: Crown,\n  github: Github,\n  filter: Filter,\n\n  // 自定义图标\n  logo: ({ className, ...props }: React.SVGProps<SVGSVGElement>) => (\n    <svg\n      className={className}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      stroke=\"currentColor\"\n      strokeWidth=\"2\"\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      {...props}\n    >\n      <path d=\"M12 2L2 7l10 5 10-5-10-5z\" />\n      <path d=\"M2 17l10 5 10-5\" />\n      <path d=\"M2 12l10 5 10-5\" />\n    </svg>\n  ),\n  \n  // 应用相关图标\n  app: ({ className, ...props }: React.SVGProps<SVGSVGElement>) => (\n    <svg\n      className={className}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      stroke=\"currentColor\"\n      strokeWidth=\"2\"\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      {...props}\n    >\n      <rect x=\"3\" y=\"3\" width=\"7\" height=\"7\" />\n      <rect x=\"14\" y=\"3\" width=\"7\" height=\"7\" />\n      <rect x=\"14\" y=\"14\" width=\"7\" height=\"7\" />\n      <rect x=\"3\" y=\"14\" width=\"7\" height=\"7\" />\n    </svg>\n  ),\n  \n  // 邀请码图标\n  code: ({ className, ...props }: React.SVGProps<SVGSVGElement>) => (\n    <svg\n      className={className}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      stroke=\"currentColor\"\n      strokeWidth=\"2\"\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      {...props}\n    >\n      <polyline points=\"16,18 22,12 16,6\" />\n      <polyline points=\"8,6 2,12 8,18\" />\n    </svg>\n  ),\n  \n  // 活动图标\n  activity: ({ className, ...props }: React.SVGProps<SVGSVGElement>) => (\n    <svg\n      className={className}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      stroke=\"currentColor\"\n      strokeWidth=\"2\"\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      {...props}\n    >\n      <polyline points=\"22,12 18,12 15,21 9,3 6,12 2,12\" />\n    </svg>\n  ),\n}\n\nexport { Icon, iconVariants, type LucideIcon }\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;AAgGA,MAAM,eAAe,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACrB,2CACA;IACE,UAAU;QACR,MAAM;YACJ,IAAI;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,OAAO;QACT;QACA,SAAS;YACP,SAAS;YACT,SAAS;YACT,WAAW;YACX,OAAO;YACP,SAAS;YACT,SAAS;YACT,OAAO;YACP,MAAM;QACR;IACF;IACA,iBAAiB;QACf,MAAM;QACN,SAAS;IACX;AACF;AAUF,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC1B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,aAAa,EAAE,WAAW,KAAK,EAAE,GAAG,OAAO,EAAE,oBAC9E,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAAE;YAAM;YAAS;QAAU,IAAI,YAAY;QACrE,GAAG,KAAK;kBAET,cAAA,8OAAC;YAAc,WAAU;;;;;;;;;;;AAI/B,KAAK,WAAW,GAAG;AAGZ,MAAM,QAAQ;IACnB,KAAK;IACL,MAAM,mMAAA,CAAA,OAAI;IACV,QAAQ,sMAAA,CAAA,SAAM;IACd,MAAM,kMAAA,CAAA,OAAI;IACV,UAAU,0MAAA,CAAA,WAAQ;IAClB,MAAM,kMAAA,CAAA,OAAI;IACV,OAAO,4LAAA,CAAA,IAAC;IACR,aAAa,oNAAA,CAAA,cAAW;IACxB,WAAW,gNAAA,CAAA,YAAS;IACpB,aAAa,oNAAA,CAAA,cAAW;IACxB,cAAc,sNAAA,CAAA,eAAY;IAC1B,WAAW,gNAAA,CAAA,YAAS;IACpB,YAAY,kNAAA,CAAA,aAAU;IACtB,aAAa,wNAAA,CAAA,cAAW;IAExB,KAAK;IACL,UAAU,0MAAA,CAAA,WAAQ;IAClB,QAAQ,sMAAA,CAAA,SAAM;IACd,OAAO,oMAAA,CAAA,QAAK;IACZ,OAAO,oMAAA,CAAA,QAAK;IACZ,MAAM,kMAAA,CAAA,OAAI;IACV,UAAU,0MAAA,CAAA,WAAQ;IAClB,KAAK,gMAAA,CAAA,MAAG;IACR,QAAQ,0MAAA,CAAA,SAAM;IACd,MAAM,2MAAA,CAAA,OAAI;IACV,OAAO,0MAAA,CAAA,SAAM;IACb,MAAM,kMAAA,CAAA,OAAI;IACV,OAAO,oMAAA,CAAA,QAAK;IACZ,MAAM,kMAAA,CAAA,OAAI;IACV,OAAO,oMAAA,CAAA,QAAK;IAEZ,KAAK;IACL,aAAa,2NAAA,CAAA,cAAW;IACxB,aAAa,oNAAA,CAAA,cAAW;IACxB,MAAM,kMAAA,CAAA,OAAI;IACV,SAAS,wNAAA,CAAA,gBAAa;IACtB,OAAO,oMAAA,CAAA,QAAK;IACZ,UAAU,0MAAA,CAAA,WAAQ;IAClB,QAAQ,0MAAA,CAAA,SAAM;IACd,OAAO,oMAAA,CAAA,QAAK;IACZ,MAAM,kMAAA,CAAA,OAAI;IACV,OAAO,oMAAA,CAAA,QAAK;IAEZ,KAAK;IACL,MAAM,kMAAA,CAAA,OAAI;IACV,OAAO,oMAAA,CAAA,QAAK;IACZ,QAAQ,4MAAA,CAAA,UAAO;IACf,WAAW,4MAAA,CAAA,UAAO;IAClB,OAAO,oMAAA,CAAA,QAAK;IACZ,OAAO,oMAAA,CAAA,QAAK;IACZ,OAAO,oMAAA,CAAA,QAAK;IACZ,MAAM,kMAAA,CAAA,OAAI;IACV,UAAU,8MAAA,CAAA,WAAQ;IAElB,KAAK;IACL,SAAS,wNAAA,CAAA,gBAAa;IACtB,MAAM,kMAAA,CAAA,OAAI;IACV,UAAU,8MAAA,CAAA,WAAQ;IAClB,YAAY,kNAAA,CAAA,aAAU;IACtB,OAAO,oMAAA,CAAA,QAAK;IACZ,UAAU,8MAAA,CAAA,WAAQ;IAElB,KAAK;IACL,MAAM,sNAAA,CAAA,eAAY;IAClB,YAAY,kNAAA,CAAA,aAAU;IACtB,QAAQ,kNAAA,CAAA,aAAU;IAClB,YAAY,kNAAA,CAAA,aAAU;IACtB,cAAc,sNAAA,CAAA,eAAY;IAC1B,UAAU,kNAAA,CAAA,YAAS;IACnB,UAAU,8MAAA,CAAA,WAAQ;IAElB,KAAK;IACL,SAAS,iNAAA,CAAA,UAAO;IAChB,SAAS,gNAAA,CAAA,YAAS;IAClB,OAAO,oMAAA,CAAA,QAAK;IACZ,MAAM,kMAAA,CAAA,OAAI;IACV,SAAS,4MAAA,CAAA,UAAO;IAChB,SAAS,wMAAA,CAAA,UAAO;IAChB,QAAQ,sMAAA,CAAA,SAAM;IACd,QAAQ,sMAAA,CAAA,SAAM;IACd,MAAM,kMAAA,CAAA,OAAI;IACV,MAAM,kMAAA,CAAA,OAAI;IACV,MAAM,kMAAA,CAAA,OAAI;IACV,MAAM,kMAAA,CAAA,OAAI;IACV,KAAK,gMAAA,CAAA,MAAG;IACR,WAAW,4MAAA,CAAA,YAAS;IACpB,QAAQ,sMAAA,CAAA,SAAM;IACd,KAAK,gMAAA,CAAA,MAAG;IACR,OAAO,oMAAA,CAAA,QAAK;IACZ,QAAQ,sMAAA,CAAA,SAAM;IACd,QAAQ,sMAAA,CAAA,SAAM;IAEd,QAAQ;IACR,MAAM,CAAC,EAAE,SAAS,EAAE,GAAG,OAAsC,iBAC3D,8OAAC;YACC,WAAW;YACX,SAAQ;YACR,MAAK;YACL,QAAO;YACP,aAAY;YACZ,eAAc;YACd,gBAAe;YACd,GAAG,KAAK;;8BAET,8OAAC;oBAAK,GAAE;;;;;;8BACR,8OAAC;oBAAK,GAAE;;;;;;8BACR,8OAAC;oBAAK,GAAE;;;;;;;;;;;;IAIZ,SAAS;IACT,KAAK,CAAC,EAAE,SAAS,EAAE,GAAG,OAAsC,iBAC1D,8OAAC;YACC,WAAW;YACX,SAAQ;YACR,MAAK;YACL,QAAO;YACP,aAAY;YACZ,eAAc;YACd,gBAAe;YACd,GAAG,KAAK;;8BAET,8OAAC;oBAAK,GAAE;oBAAI,GAAE;oBAAI,OAAM;oBAAI,QAAO;;;;;;8BACnC,8OAAC;oBAAK,GAAE;oBAAK,GAAE;oBAAI,OAAM;oBAAI,QAAO;;;;;;8BACpC,8OAAC;oBAAK,GAAE;oBAAK,GAAE;oBAAK,OAAM;oBAAI,QAAO;;;;;;8BACrC,8OAAC;oBAAK,GAAE;oBAAI,GAAE;oBAAK,OAAM;oBAAI,QAAO;;;;;;;;;;;;IAIxC,QAAQ;IACR,MAAM,CAAC,EAAE,SAAS,EAAE,GAAG,OAAsC,iBAC3D,8OAAC;YACC,WAAW;YACX,SAAQ;YACR,MAAK;YACL,QAAO;YACP,aAAY;YACZ,eAAc;YACd,gBAAe;YACd,GAAG,KAAK;;8BAET,8OAAC;oBAAS,QAAO;;;;;;8BACjB,8OAAC;oBAAS,QAAO;;;;;;;;;;;;IAIrB,OAAO;IACP,UAAU,CAAC,EAAE,SAAS,EAAE,GAAG,OAAsC,iBAC/D,8OAAC;YACC,WAAW;YACX,SAAQ;YACR,MAAK;YACL,QAAO;YACP,aAAY;YACZ,eAAc;YACd,gBAAe;YACd,GAAG,KAAK;sBAET,cAAA,8OAAC;gBAAS,QAAO;;;;;;;;;;;AAGvB", "debugId": null}}, {"offset": {"line": 870, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/app/not-found.tsx"], "sourcesContent": ["import Link from \"next/link\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Container, Center } from \"@/components/ui/layout\"\nimport { Icons } from \"@/components/ui/icon\"\n\nexport default function NotFound() {\n  return (\n    <Container className=\"min-h-screen\">\n      <Center className=\"min-h-screen\">\n        <Card className=\"w-full max-w-md text-center\">\n          <CardHeader>\n            <div className=\"w-16 h-16 bg-warning/10 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <Icons.search className=\"w-8 h-8 text-warning\" />\n            </div>\n            <CardTitle className=\"text-xl\">页面未找到</CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <p className=\"text-muted-foreground\">\n              抱歉，您访问的页面不存在或已被移动。\n            </p>\n            <div className=\"flex gap-3\">\n              <Button asChild className=\"flex-1\">\n                <Link href=\"/\">\n                  <Icons.home className=\"w-4 h-4 mr-2\" />\n                  返回首页\n                </Link>\n              </Button>\n              <Button variant=\"outline\" asChild className=\"flex-1\">\n                <Link href=\"/apps\">\n                  <Icons.app className=\"w-4 h-4 mr-2\" />\n                  浏览应用\n                </Link>\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n      </Center>\n    </Container>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC,kIAAA,CAAA,YAAS;QAAC,WAAU;kBACnB,cAAA,8OAAC,kIAAA,CAAA,SAAM;YAAC,WAAU;sBAChB,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,gIAAA,CAAA,QAAK,CAAC,MAAM;oCAAC,WAAU;;;;;;;;;;;0CAE1B,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAU;;;;;;;;;;;;kCAEjC,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;0CAGrC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,OAAO;wCAAC,WAAU;kDACxB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;;8DACT,8OAAC,gIAAA,CAAA,QAAK,CAAC,IAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAI3C,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,OAAO;wCAAC,WAAU;kDAC1C,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;;8DACT,8OAAC,gIAAA,CAAA,QAAK,CAAC,GAAG;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUxD", "debugId": null}}]}