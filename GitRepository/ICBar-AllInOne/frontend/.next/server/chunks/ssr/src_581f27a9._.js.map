{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/utils/index.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\n// 合并CSS类名\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// 格式化日期\nexport function formatDate(date: string | Date): string {\n  const d = new Date(date);\n  return d.toLocaleDateString('zh-CN', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  });\n}\n\n// 格式化相对时间\nexport function formatRelativeTime(date: string | Date): string {\n  const now = new Date();\n  const target = new Date(date);\n  const diffInSeconds = Math.floor((now.getTime() - target.getTime()) / 1000);\n\n  if (diffInSeconds < 60) {\n    return '刚刚';\n  } else if (diffInSeconds < 3600) {\n    const minutes = Math.floor(diffInSeconds / 60);\n    return `${minutes}分钟前`;\n  } else if (diffInSeconds < 86400) {\n    const hours = Math.floor(diffInSeconds / 3600);\n    return `${hours}小时前`;\n  } else if (diffInSeconds < 2592000) {\n    const days = Math.floor(diffInSeconds / 86400);\n    return `${days}天前`;\n  } else {\n    return formatDate(date);\n  }\n}\n\n// 生成随机字符串\nexport function generateRandomString(length: number): string {\n  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n  let result = '';\n  for (let i = 0; i < length; i++) {\n    result += chars.charAt(Math.floor(Math.random() * chars.length));\n  }\n  return result;\n}\n\n// 验证邮箱格式\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\n// 截断文本\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength) + '...';\n}\n\n// 应用分类映射\nexport const categoryMap: Record<string, string> = {\n  productivity: '效率工具',\n  entertainment: '娱乐',\n  education: '教育',\n  business: '商务',\n  lifestyle: '生活方式',\n  utilities: '实用工具',\n  games: '游戏',\n  social: '社交',\n  other: '其他'\n};\n\n// 获取分类显示名称\nexport function getCategoryDisplayName(category: string): string {\n  return categoryMap[category] || category;\n}\n\n// 验证URL格式\nexport function isValidUrl(url: string): boolean {\n  try {\n    new URL(url);\n    return true;\n  } catch {\n    return false;\n  }\n}\n\n// 计算活动热度分数\nexport function calculateActivityScore(\n  activityCount: number,\n  totalCodes: number,\n  createdAt: string\n): number {\n  const daysSinceCreation = Math.floor(\n    (Date.now() - new Date(createdAt).getTime()) / (1000 * 60 * 60 * 24)\n  );\n  \n  // 基础分数：活动数量 * 2 + 总邀请码数量\n  const baseScore = activityCount * 2 + totalCodes;\n  \n  // 时间衰减因子（越新的应用分数越高）\n  const timeDecay = Math.max(0.1, 1 - daysSinceCreation / 365);\n  \n  return Math.round(baseScore * timeDecay);\n}\n\n// 防抖函数\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n// 节流函数\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n}\n\n// 格式化数字为可读格式\nexport function formatNumber(num: number): string {\n  if (num >= 1000000) {\n    return (num / 1000000).toFixed(1) + 'M'\n  }\n  if (num >= 1000) {\n    return (num / 1000).toFixed(1) + 'K'\n  }\n  return num.toString()\n}\n\n// 深拷贝对象\nexport function deepClone<T>(obj: T): T {\n  if (obj === null || typeof obj !== 'object') return obj\n  if (obj instanceof Date) return new Date(obj.getTime()) as T\n  if (obj instanceof Array) return obj.map(item => deepClone(item)) as T\n  if (typeof obj === 'object') {\n    const clonedObj = {} as T\n    for (const key in obj) {\n      if (obj.hasOwnProperty(key)) {\n        clonedObj[key] = deepClone(obj[key])\n      }\n    }\n    return clonedObj\n  }\n  return obj\n}\n\n// 检查是否为移动设备\nexport function isMobile(): boolean {\n  if (typeof window === 'undefined') return false\n  return window.innerWidth < 768\n}\n\n// 平滑滚动到元素\nexport function scrollToElement(elementId: string, offset: number = 0): void {\n  const element = document.getElementById(elementId)\n  if (element) {\n    const top = element.offsetTop - offset\n    window.scrollTo({\n      top,\n      behavior: 'smooth'\n    })\n  }\n}\n\n// 复制文本到剪贴板\nexport async function copyToClipboard(text: string): Promise<boolean> {\n  try {\n    await navigator.clipboard.writeText(text)\n    return true\n  } catch (err) {\n    // 降级方案\n    const textArea = document.createElement('textarea')\n    textArea.value = text\n    document.body.appendChild(textArea)\n    textArea.select()\n    const success = document.execCommand('copy')\n    document.body.removeChild(textArea)\n    return success\n  }\n}\n\n// 获取文件大小的可读格式\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes'\n\n  const k = 1024\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n\n// 获取颜色的对比色\nexport function getContrastColor(hexColor: string): string {\n  // 移除 # 符号\n  const hex = hexColor.replace('#', '')\n\n  // 转换为 RGB\n  const r = parseInt(hex.substr(0, 2), 16)\n  const g = parseInt(hex.substr(2, 2), 16)\n  const b = parseInt(hex.substr(4, 2), 16)\n\n  // 计算亮度\n  const brightness = (r * 299 + g * 587 + b * 114) / 1000\n\n  // 返回对比色\n  return brightness > 128 ? '#000000' : '#ffffff'\n}\n\n// 生成随机ID\nexport function generateId(): string {\n  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)\n}\n\n// 格式化价格\nexport function formatPrice(price: number, currency: string = '¥'): string {\n  return `${currency}${price.toFixed(2)}`\n}\n\n// 获取设备类型\nexport function getDeviceType(): 'mobile' | 'tablet' | 'desktop' {\n  if (typeof window === 'undefined') return 'desktop'\n\n  const width = window.innerWidth\n  if (width < 768) return 'mobile'\n  if (width < 1024) return 'tablet'\n  return 'desktop'\n}\n\n// 检查是否支持WebP\nexport function supportsWebP(): Promise<boolean> {\n  return new Promise((resolve) => {\n    const webP = new Image()\n    webP.onload = webP.onerror = () => {\n      resolve(webP.height === 2)\n    }\n    webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA'\n  })\n}\n\n// 本地存储工具\nexport const storage = {\n  get: <T>(key: string, defaultValue?: T): T | null => {\n    if (typeof window === 'undefined') return defaultValue || null\n    try {\n      const item = localStorage.getItem(key)\n      return item ? JSON.parse(item) : defaultValue || null\n    } catch {\n      return defaultValue || null\n    }\n  },\n\n  set: <T>(key: string, value: T): void => {\n    if (typeof window === 'undefined') return\n    try {\n      localStorage.setItem(key, JSON.stringify(value))\n    } catch (error) {\n      console.error('Failed to save to localStorage:', error)\n    }\n  },\n\n  remove: (key: string): void => {\n    if (typeof window === 'undefined') return\n    localStorage.removeItem(key)\n  },\n\n  clear: (): void => {\n    if (typeof window === 'undefined') return\n    localStorage.clear()\n  }\n}\n\n// 生成头像的工具函数\nexport function generateAvatar(name: string, size: number = 64, colors?: string[]): string {\n  const defaultColors = [\n    '#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6',\n    '#06b6d4', '#84cc16', '#f97316', '#ec4899', '#6366f1'\n  ]\n\n  const colorPalette = colors || defaultColors\n  const initials = name\n    .split(' ')\n    .map(word => word[0])\n    .join('')\n    .toUpperCase()\n    .slice(0, 2)\n\n  // 根据名字生成一致的颜色\n  const colorIndex = name.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0) % colorPalette.length\n  const color = colorPalette[colorIndex]\n\n  const svg = `\n    <svg width=\"${size}\" height=\"${size}\" xmlns=\"http://www.w3.org/2000/svg\">\n      <defs>\n        <linearGradient id=\"grad-${name.replace(/\\s+/g, '')}\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n          <stop offset=\"0%\" style=\"stop-color:${color};stop-opacity:1\" />\n          <stop offset=\"100%\" style=\"stop-color:${color}dd;stop-opacity:1\" />\n        </linearGradient>\n      </defs>\n      <circle cx=\"${size/2}\" cy=\"${size/2}\" r=\"${size/2}\" fill=\"url(#grad-${name.replace(/\\s+/g, '')})\"/>\n      <text x=\"50%\" y=\"50%\" text-anchor=\"middle\" dy=\"0.3em\" fill=\"white\" font-family=\"system-ui, sans-serif\" font-size=\"${size * 0.3}\" font-weight=\"600\">\n        ${initials}\n      </text>\n    </svg>\n  `.trim()\n\n  return `data:image/svg+xml,${encodeURIComponent(svg)}`\n}\n\n// 生成应用图标的工具函数\nexport function generateAppIcon(name: string, color?: string, size: number = 64): string {\n  const defaultColor = '#3b82f6'\n  const iconColor = color || defaultColor\n  const initials = name\n    .split(' ')\n    .map(word => word[0])\n    .join('')\n    .toUpperCase()\n    .slice(0, 2)\n\n  const svg = `\n    <svg width=\"${size}\" height=\"${size}\" xmlns=\"http://www.w3.org/2000/svg\">\n      <defs>\n        <linearGradient id=\"app-grad-${name.replace(/\\s+/g, '')}\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n          <stop offset=\"0%\" style=\"stop-color:${iconColor};stop-opacity:1\" />\n          <stop offset=\"100%\" style=\"stop-color:${iconColor}dd;stop-opacity:1\" />\n        </linearGradient>\n      </defs>\n      <rect width=\"100%\" height=\"100%\" fill=\"url(#app-grad-${name.replace(/\\s+/g, '')})\" rx=\"${size * 0.2}\"/>\n      <text x=\"50%\" y=\"50%\" text-anchor=\"middle\" dy=\"0.3em\" fill=\"white\" font-family=\"system-ui, sans-serif\" font-size=\"${size * 0.3}\" font-weight=\"600\">\n        ${initials}\n      </text>\n    </svg>\n  `.trim()\n\n  return `data:image/svg+xml,${encodeURIComponent(svg)}`\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAGO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAGO,SAAS,mBAAmB,IAAmB;IACpD,MAAM,MAAM,IAAI;IAChB,MAAM,SAAS,IAAI,KAAK;IACxB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,OAAO,OAAO,EAAE,IAAI;IAEtE,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT,OAAO,IAAI,gBAAgB,MAAM;QAC/B,MAAM,UAAU,KAAK,KAAK,CAAC,gBAAgB;QAC3C,OAAO,GAAG,QAAQ,GAAG,CAAC;IACxB,OAAO,IAAI,gBAAgB,OAAO;QAChC,MAAM,QAAQ,KAAK,KAAK,CAAC,gBAAgB;QACzC,OAAO,GAAG,MAAM,GAAG,CAAC;IACtB,OAAO,IAAI,gBAAgB,SAAS;QAClC,MAAM,OAAO,KAAK,KAAK,CAAC,gBAAgB;QACxC,OAAO,GAAG,KAAK,EAAE,CAAC;IACpB,OAAO;QACL,OAAO,WAAW;IACpB;AACF;AAGO,SAAS,qBAAqB,MAAc;IACjD,MAAM,QAAQ;IACd,IAAI,SAAS;IACb,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,UAAU,MAAM,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM;IAChE;IACA,OAAO;AACT;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAGO,MAAM,cAAsC;IACjD,cAAc;IACd,eAAe;IACf,WAAW;IACX,UAAU;IACV,WAAW;IACX,WAAW;IACX,OAAO;IACP,QAAQ;IACR,OAAO;AACT;AAGO,SAAS,uBAAuB,QAAgB;IACrD,OAAO,WAAW,CAAC,SAAS,IAAI;AAClC;AAGO,SAAS,WAAW,GAAW;IACpC,IAAI;QACF,IAAI,IAAI;QACR,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAGO,SAAS,uBACd,aAAqB,EACrB,UAAkB,EAClB,SAAiB;IAEjB,MAAM,oBAAoB,KAAK,KAAK,CAClC,CAAC,KAAK,GAAG,KAAK,IAAI,KAAK,WAAW,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;IAGrE,yBAAyB;IACzB,MAAM,YAAY,gBAAgB,IAAI;IAEtC,oBAAoB;IACpB,MAAM,YAAY,KAAK,GAAG,CAAC,KAAK,IAAI,oBAAoB;IAExD,OAAO,KAAK,KAAK,CAAC,YAAY;AAChC;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF;AAGO,SAAS,aAAa,GAAW;IACtC,IAAI,OAAO,SAAS;QAClB,OAAO,CAAC,MAAM,OAAO,EAAE,OAAO,CAAC,KAAK;IACtC;IACA,IAAI,OAAO,MAAM;QACf,OAAO,CAAC,MAAM,IAAI,EAAE,OAAO,CAAC,KAAK;IACnC;IACA,OAAO,IAAI,QAAQ;AACrB;AAGO,SAAS,UAAa,GAAM;IACjC,IAAI,QAAQ,QAAQ,OAAO,QAAQ,UAAU,OAAO;IACpD,IAAI,eAAe,MAAM,OAAO,IAAI,KAAK,IAAI,OAAO;IACpD,IAAI,eAAe,OAAO,OAAO,IAAI,GAAG,CAAC,CAAA,OAAQ,UAAU;IAC3D,IAAI,OAAO,QAAQ,UAAU;QAC3B,MAAM,YAAY,CAAC;QACnB,IAAK,MAAM,OAAO,IAAK;YACrB,IAAI,IAAI,cAAc,CAAC,MAAM;gBAC3B,SAAS,CAAC,IAAI,GAAG,UAAU,GAAG,CAAC,IAAI;YACrC;QACF;QACA,OAAO;IACT;IACA,OAAO;AACT;AAGO,SAAS;IACd,wCAAmC,OAAO;;AAE5C;AAGO,SAAS,gBAAgB,SAAiB,EAAE,SAAiB,CAAC;IACnE,MAAM,UAAU,SAAS,cAAc,CAAC;IACxC,IAAI,SAAS;QACX,MAAM,MAAM,QAAQ,SAAS,GAAG;QAChC,OAAO,QAAQ,CAAC;YACd;YACA,UAAU;QACZ;IACF;AACF;AAGO,eAAe,gBAAgB,IAAY;IAChD,IAAI;QACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;QACpC,OAAO;IACT,EAAE,OAAO,KAAK;QACZ,OAAO;QACP,MAAM,WAAW,SAAS,aAAa,CAAC;QACxC,SAAS,KAAK,GAAG;QACjB,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,SAAS,MAAM;QACf,MAAM,UAAU,SAAS,WAAW,CAAC;QACrC,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,OAAO;IACT;AACF;AAGO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;KAAK;IAC/C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAGO,SAAS,iBAAiB,QAAgB;IAC/C,UAAU;IACV,MAAM,MAAM,SAAS,OAAO,CAAC,KAAK;IAElC,UAAU;IACV,MAAM,IAAI,SAAS,IAAI,MAAM,CAAC,GAAG,IAAI;IACrC,MAAM,IAAI,SAAS,IAAI,MAAM,CAAC,GAAG,IAAI;IACrC,MAAM,IAAI,SAAS,IAAI,MAAM,CAAC,GAAG,IAAI;IAErC,OAAO;IACP,MAAM,aAAa,CAAC,IAAI,MAAM,IAAI,MAAM,IAAI,GAAG,IAAI;IAEnD,QAAQ;IACR,OAAO,aAAa,MAAM,YAAY;AACxC;AAGO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;AAC/F;AAGO,SAAS,YAAY,KAAa,EAAE,WAAmB,GAAG;IAC/D,OAAO,GAAG,WAAW,MAAM,OAAO,CAAC,IAAI;AACzC;AAGO,SAAS;IACd,wCAAmC,OAAO;;IAE1C,MAAM;AAIR;AAGO,SAAS;IACd,OAAO,IAAI,QAAQ,CAAC;QAClB,MAAM,OAAO,IAAI;QACjB,KAAK,MAAM,GAAG,KAAK,OAAO,GAAG;YAC3B,QAAQ,KAAK,MAAM,KAAK;QAC1B;QACA,KAAK,GAAG,GAAG;IACb;AACF;AAGO,MAAM,UAAU;IACrB,KAAK,CAAI,KAAa;QACpB,wCAAmC,OAAO,gBAAgB;;IAO5D;IAEA,KAAK,CAAI,KAAa;QACpB,wCAAmC;;IAMrC;IAEA,QAAQ,CAAC;QACP,wCAAmC;;IAErC;IAEA,OAAO;QACL,wCAAmC;;IAErC;AACF;AAGO,SAAS,eAAe,IAAY,EAAE,OAAe,EAAE,EAAE,MAAiB;IAC/E,MAAM,gBAAgB;QACpB;QAAW;QAAW;QAAW;QAAW;QAC5C;QAAW;QAAW;QAAW;QAAW;KAC7C;IAED,MAAM,eAAe,UAAU;IAC/B,MAAM,WAAW,KACd,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,EAAE,EACnB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IAEZ,cAAc;IACd,MAAM,aAAa,KAAK,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,UAAU,CAAC,IAAI,KAAK,aAAa,MAAM;IAC1G,MAAM,QAAQ,YAAY,CAAC,WAAW;IAEtC,MAAM,MAAM,CAAC;gBACC,EAAE,KAAK,UAAU,EAAE,KAAK;;iCAEP,EAAE,KAAK,OAAO,CAAC,QAAQ,IAAI;8CACd,EAAE,MAAM;gDACN,EAAE,MAAM;;;kBAGtC,EAAE,OAAK,EAAE,MAAM,EAAE,OAAK,EAAE,KAAK,EAAE,OAAK,EAAE,kBAAkB,EAAE,KAAK,OAAO,CAAC,QAAQ,IAAI;wHACmB,EAAE,OAAO,IAAI;QAC7H,EAAE,SAAS;;;EAGjB,CAAC,CAAC,IAAI;IAEN,OAAO,CAAC,mBAAmB,EAAE,mBAAmB,MAAM;AACxD;AAGO,SAAS,gBAAgB,IAAY,EAAE,KAAc,EAAE,OAAe,EAAE;IAC7E,MAAM,eAAe;IACrB,MAAM,YAAY,SAAS;IAC3B,MAAM,WAAW,KACd,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,EAAE,EACnB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IAEZ,MAAM,MAAM,CAAC;gBACC,EAAE,KAAK,UAAU,EAAE,KAAK;;qCAEH,EAAE,KAAK,OAAO,CAAC,QAAQ,IAAI;8CAClB,EAAE,UAAU;gDACV,EAAE,UAAU;;;2DAGD,EAAE,KAAK,OAAO,CAAC,QAAQ,IAAI,OAAO,EAAE,OAAO,IAAI;wHACc,EAAE,OAAO,IAAI;QAC7H,EAAE,SAAS;;;EAGjB,CAAC,CAAC,IAAI;IAEN,OAAO,CAAC,mBAAmB,EAAE,mBAAmB,MAAM;AACxD", "debugId": null}}, {"offset": {"line": 308, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/components/ui/loading.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/utils\"\n\nconst loadingVariants = cva(\n  \"animate-spin rounded-full border-2 border-current border-t-transparent\",\n  {\n    variants: {\n      size: {\n        sm: \"h-4 w-4\",\n        default: \"h-6 w-6\",\n        lg: \"h-8 w-8\",\n        xl: \"h-12 w-12\",\n      },\n      variant: {\n        default: \"text-primary\",\n        secondary: \"text-secondary\",\n        muted: \"text-muted-foreground\",\n      },\n    },\n    defaultVariants: {\n      size: \"default\",\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface LoadingProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof loadingVariants> {\n  text?: string\n}\n\nconst Loading = React.forwardRef<HTMLDivElement, LoadingProps>(\n  ({ className, size, variant, text, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\"flex items-center justify-center gap-2\", className)}\n      {...props}\n    >\n      <div className={cn(loadingVariants({ size, variant }))} />\n      {text && <span className=\"text-sm text-muted-foreground\">{text}</span>}\n    </div>\n  )\n)\nLoading.displayName = \"Loading\"\n\n// 骨架屏组件\nconst Skeleton = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"animate-pulse rounded-md bg-muted\", className)}\n    {...props}\n  />\n))\nSkeleton.displayName = \"Skeleton\"\n\n// 页面加载组件\nconst PageLoading: React.FC<{ text?: string }> = ({ text = \"加载中...\" }) => (\n  <div className=\"flex min-h-[400px] items-center justify-center\">\n    <Loading size=\"lg\" text={text} />\n  </div>\n)\n\n// 按钮加载组件\nconst ButtonLoading: React.FC = () => (\n  <div className=\"flex items-center gap-2\">\n    <Loading size=\"sm\" />\n    <span>处理中...</span>\n  </div>\n)\n\n// 卡片骨架屏\nconst CardSkeleton: React.FC = () => (\n  <div className=\"space-y-3\">\n    <Skeleton className=\"h-4 w-3/4\" />\n    <Skeleton className=\"h-4 w-1/2\" />\n    <Skeleton className=\"h-20 w-full\" />\n  </div>\n)\n\n// 列表骨架屏\nconst ListSkeleton: React.FC<{ count?: number }> = ({ count = 3 }) => (\n  <div className=\"space-y-4\">\n    {Array.from({ length: count }).map((_, i) => (\n      <div key={i} className=\"flex items-center space-x-4\">\n        <Skeleton className=\"h-12 w-12 rounded-full\" />\n        <div className=\"space-y-2 flex-1\">\n          <Skeleton className=\"h-4 w-3/4\" />\n          <Skeleton className=\"h-4 w-1/2\" />\n        </div>\n      </div>\n    ))}\n  </div>\n)\n\nexport { \n  Loading, \n  Skeleton, \n  PageLoading, \n  ButtonLoading, \n  CardSkeleton, \n  ListSkeleton,\n  loadingVariants \n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,kBAAkB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACxB,0EACA;IACE,UAAU;QACR,MAAM;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;QACA,SAAS;YACP,SAAS;YACT,WAAW;YACX,OAAO;QACT;IACF;IACA,iBAAiB;QACf,MAAM;QACN,SAAS;IACX;AACF;AASF,MAAM,wBAAU,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC7B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE,oBAC7C,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;0BAET,8OAAC;gBAAI,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB;oBAAE;oBAAM;gBAAQ;;;;;;YAClD,sBAAQ,8OAAC;gBAAK,WAAU;0BAAiC;;;;;;;;;;;;AAIhE,QAAQ,WAAW,GAAG;AAEtB,QAAQ;AACR,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG;AAEvB,SAAS;AACT,MAAM,cAA2C,CAAC,EAAE,OAAO,QAAQ,EAAE,iBACnE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAQ,MAAK;YAAK,MAAM;;;;;;;;;;;AAI7B,SAAS;AACT,MAAM,gBAA0B,kBAC9B,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAQ,MAAK;;;;;;0BACd,8OAAC;0BAAK;;;;;;;;;;;;AAIV,QAAQ;AACR,MAAM,eAAyB,kBAC7B,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAS,WAAU;;;;;;0BACpB,8OAAC;gBAAS,WAAU;;;;;;0BACpB,8OAAC;gBAAS,WAAU;;;;;;;;;;;;AAIxB,QAAQ;AACR,MAAM,eAA6C,CAAC,EAAE,QAAQ,CAAC,EAAE,iBAC/D,8OAAC;QAAI,WAAU;kBACZ,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,GAAG,CAAC,CAAC,GAAG,kBACrC,8OAAC;gBAAY,WAAU;;kCACrB,8OAAC;wBAAS,WAAU;;;;;;kCACpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAS,WAAU;;;;;;0CACpB,8OAAC;gCAAS,WAAU;;;;;;;;;;;;;eAJd", "debugId": null}}, {"offset": {"line": 512, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/app/loading.tsx"], "sourcesContent": ["import { PageLoading } from \"@/components/ui/loading\"\n\nexport default function Loading() {\n  return <PageLoading text=\"正在加载...\" />\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBAAO,8OAAC,mIAAA,CAAA,cAAW;QAAC,MAAK;;;;;;AAC3B", "debugId": null}}]}