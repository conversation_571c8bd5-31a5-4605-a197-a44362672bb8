{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/components/layout/header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Header = registerClientReference(\n    function() { throw new Error(\"Attempted to call Head<PERSON>() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/header.tsx <module evaluation>\",\n    \"Header\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,kEACA", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/components/layout/header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Header = registerClientReference(\n    function() { throw new Error(\"Attempted to call Header() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/header.tsx\",\n    \"Header\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,8CACA", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/components/layout/footer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Footer = registerClientReference(\n    function() { throw new Error(\"Attempted to call Footer() from the server but Foot<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/footer.tsx <module evaluation>\",\n    \"Footer\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,kEACA", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/components/layout/footer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Footer = registerClientReference(\n    function() { throw new Error(\"Attempted to call Footer() from the server but Foot<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/footer.tsx\",\n    \"Footer\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,8CACA", "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 118, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-error text-error-foreground hover:bg-error/80\",\n        outline: \"text-foreground\",\n        success:\n          \"border-transparent bg-success text-success-foreground hover:bg-success/80\",\n        warning:\n          \"border-transparent bg-warning text-warning-foreground hover:bg-warning/80\",\n        info:\n          \"border-transparent bg-info text-info-foreground hover:bg-info/80\",\n      },\n      size: {\n        default: \"px-2.5 py-0.5 text-xs\",\n        sm: \"px-2 py-0.5 text-xs\",\n        lg: \"px-3 py-1 text-sm\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, size, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant, size }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;YACT,SACE;YACF,SACE;YACF,MACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,OAAmB;IAC/D,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;YAAS;QAAK,IAAI;QAAa,GAAG,KAAK;;;;;;AAE9E", "debugId": null}}, {"offset": {"line": 170, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/app/apps/page.tsx"], "sourcesContent": ["import { Header } from \"@/components/layout/header\"\nimport { Footer } from \"@/components/layout/footer\"\nimport { Container, Grid } from \"@/components/ui/layout\"\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Button } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Icons } from \"@/components/ui/icon\"\n\nexport default function AppsPage() {\n  const apps = [\n    {\n      id: 1,\n      name: \"Notion\",\n      description: \"一体化工作空间，集笔记、任务管理、数据库于一身\",\n      category: \"效率工具\",\n      rating: 4.8,\n      downloads: \"10M+\",\n      image: \"/api/placeholder/80/80\",\n      tags: [\"笔记\", \"协作\", \"数据库\"],\n      featured: true\n    },\n    {\n      id: 2,\n      name: \"Figma\",\n      description: \"协作式界面设计工具，支持实时多人编辑\",\n      category: \"设计工具\",\n      rating: 4.9,\n      downloads: \"5M+\",\n      image: \"/api/placeholder/80/80\",\n      tags: [\"设计\", \"协作\", \"原型\"],\n      featured: true\n    },\n    {\n      id: 3,\n      name: \"Discord\",\n      description: \"为游戏玩家和社区打造的语音、视频和文字聊天应用\",\n      category: \"社交通讯\",\n      rating: 4.7,\n      downloads: \"100M+\",\n      image: \"/api/placeholder/80/80\",\n      tags: [\"聊天\", \"语音\", \"社区\"],\n      featured: false\n    },\n    {\n      id: 4,\n      name: \"Spotify\",\n      description: \"全球领先的音乐流媒体平台\",\n      category: \"音乐娱乐\",\n      rating: 4.6,\n      downloads: \"1B+\",\n      image: \"/api/placeholder/80/80\",\n      tags: [\"音乐\", \"播客\", \"流媒体\"],\n      featured: false\n    }\n  ]\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      <Header />\n      \n      <main className=\"py-8\">\n        <Container>\n          {/* 页面标题 */}\n          <div className=\"mb-8\">\n            <h1 className=\"text-3xl font-bold mb-2\">应用推荐</h1>\n            <p className=\"text-muted-foreground\">\n              发现优质应用，提升工作和生活效率\n            </p>\n          </div>\n\n          {/* 筛选和搜索 */}\n          <div className=\"flex flex-col sm:flex-row gap-4 mb-8\">\n            <div className=\"flex gap-2 flex-wrap\">\n              <Button variant=\"default\" size=\"sm\">全部</Button>\n              <Button variant=\"outline\" size=\"sm\">效率工具</Button>\n              <Button variant=\"outline\" size=\"sm\">设计工具</Button>\n              <Button variant=\"outline\" size=\"sm\">社交通讯</Button>\n              <Button variant=\"outline\" size=\"sm\">音乐娱乐</Button>\n            </div>\n            <div className=\"flex gap-2 ml-auto\">\n              <Button variant=\"outline\" size=\"sm\">\n                <Icons.filter className=\"w-4 h-4 mr-2\" />\n                筛选\n              </Button>\n              <Button variant=\"outline\" size=\"sm\">\n                <Icons.search className=\"w-4 h-4 mr-2\" />\n                搜索\n              </Button>\n            </div>\n          </div>\n\n          {/* 应用列表 */}\n          <Grid cols={1} gap=\"md\" className=\"md:grid-cols-2 lg:grid-cols-3\">\n            {apps.map((app) => (\n              <Card key={app.id} className=\"hover:shadow-lg transition-shadow\">\n                <CardHeader className=\"pb-4\">\n                  <div className=\"flex items-start gap-4\">\n                    <img \n                      src={app.image} \n                      alt={app.name}\n                      className=\"w-16 h-16 rounded-lg object-cover\"\n                    />\n                    <div className=\"flex-1 min-w-0\">\n                      <div className=\"flex items-center gap-2 mb-1\">\n                        <CardTitle className=\"text-lg truncate\">{app.name}</CardTitle>\n                        {app.featured && (\n                          <Badge variant=\"warning\" size=\"sm\">推荐</Badge>\n                        )}\n                      </div>\n                      <p className=\"text-sm text-muted-foreground mb-2\">\n                        {app.category}\n                      </p>\n                      <div className=\"flex items-center gap-4 text-sm text-muted-foreground\">\n                        <div className=\"flex items-center gap-1\">\n                          <Icons.star className=\"w-4 h-4 fill-warning text-warning\" />\n                          <span>{app.rating}</span>\n                        </div>\n                        <div className=\"flex items-center gap-1\">\n                          <Icons.download className=\"w-4 h-4\" />\n                          <span>{app.downloads}</span>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </CardHeader>\n                \n                <CardContent className=\"pt-0\">\n                  <p className=\"text-sm text-muted-foreground mb-4 line-clamp-2\">\n                    {app.description}\n                  </p>\n                  \n                  <div className=\"flex flex-wrap gap-1 mb-4\">\n                    {app.tags.map((tag) => (\n                      <Badge key={tag} variant=\"secondary\" size=\"sm\">\n                        {tag}\n                      </Badge>\n                    ))}\n                  </div>\n                  \n                  <div className=\"flex gap-2\">\n                    <Button className=\"flex-1\" size=\"sm\">\n                      <Icons.download className=\"w-4 h-4 mr-2\" />\n                      获取邀请码\n                    </Button>\n                    <Button variant=\"outline\" size=\"sm\">\n                      <Icons.eye className=\"w-4 h-4\" />\n                    </Button>\n                  </div>\n                </CardContent>\n              </Card>\n            ))}\n          </Grid>\n\n          {/* 加载更多 */}\n          <div className=\"text-center mt-8\">\n            <Button variant=\"outline\">\n              加载更多应用\n            </Button>\n          </div>\n        </Container>\n      </main>\n\n      <Footer />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAEe,SAAS;IACtB,MAAM,OAAO;QACX;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,UAAU;YACV,QAAQ;YACR,WAAW;YACX,OAAO;YACP,MAAM;gBAAC;gBAAM;gBAAM;aAAM;YACzB,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,UAAU;YACV,QAAQ;YACR,WAAW;YACX,OAAO;YACP,MAAM;gBAAC;gBAAM;gBAAM;aAAK;YACxB,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,UAAU;YACV,QAAQ;YACR,WAAW;YACX,OAAO;YACP,MAAM;gBAAC;gBAAM;gBAAM;aAAK;YACxB,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,UAAU;YACV,QAAQ;YACR,WAAW;YACX,OAAO;YACP,MAAM;gBAAC;gBAAM;gBAAM;aAAM;YACzB,UAAU;QACZ;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,SAAM;;;;;0BAEP,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kIAAA,CAAA,YAAS;;sCAER,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0B;;;;;;8CACxC,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAMvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;sDAAK;;;;;;sDACpC,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;sDAAK;;;;;;sDACpC,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;sDAAK;;;;;;sDACpC,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;sDAAK;;;;;;sDACpC,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;sDAAK;;;;;;;;;;;;8CAEtC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;;8DAC7B,8OAAC,gIAAA,CAAA,QAAK,CAAC,MAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAG3C,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;;8DAC7B,8OAAC,gIAAA,CAAA,QAAK,CAAC,MAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;sCAO/C,8OAAC,kIAAA,CAAA,OAAI;4BAAC,MAAM;4BAAG,KAAI;4BAAK,WAAU;sCAC/B,KAAK,GAAG,CAAC,CAAC,oBACT,8OAAC,gIAAA,CAAA,OAAI;oCAAc,WAAU;;sDAC3B,8OAAC,gIAAA,CAAA,aAAU;4CAAC,WAAU;sDACpB,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,KAAK,IAAI,KAAK;wDACd,KAAK,IAAI,IAAI;wDACb,WAAU;;;;;;kEAEZ,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,gIAAA,CAAA,YAAS;wEAAC,WAAU;kFAAoB,IAAI,IAAI;;;;;;oEAChD,IAAI,QAAQ,kBACX,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAU,MAAK;kFAAK;;;;;;;;;;;;0EAGvC,8OAAC;gEAAE,WAAU;0EACV,IAAI,QAAQ;;;;;;0EAEf,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,gIAAA,CAAA,QAAK,CAAC,IAAI;gFAAC,WAAU;;;;;;0FACtB,8OAAC;0FAAM,IAAI,MAAM;;;;;;;;;;;;kFAEnB,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,gIAAA,CAAA,QAAK,CAAC,QAAQ;gFAAC,WAAU;;;;;;0FAC1B,8OAAC;0FAAM,IAAI,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAO9B,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,8OAAC;oDAAE,WAAU;8DACV,IAAI,WAAW;;;;;;8DAGlB,8OAAC;oDAAI,WAAU;8DACZ,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,oBACb,8OAAC,iIAAA,CAAA,QAAK;4DAAW,SAAQ;4DAAY,MAAK;sEACvC;2DADS;;;;;;;;;;8DAMhB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kIAAA,CAAA,SAAM;4DAAC,WAAU;4DAAS,MAAK;;8EAC9B,8OAAC,gIAAA,CAAA,QAAK,CAAC,QAAQ;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAG7C,8OAAC,kIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAU,MAAK;sEAC7B,cAAA,8OAAC,gIAAA,CAAA,QAAK,CAAC,GAAG;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;mCAnDlB,IAAI,EAAE;;;;;;;;;;sCA4DrB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;0CAAU;;;;;;;;;;;;;;;;;;;;;;0BAOhC,8OAAC,sIAAA,CAAA,SAAM;;;;;;;;;;;AAGb", "debugId": null}}, {"offset": {"line": 679, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 717, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAMhG,8BAA8B;AAG9B,OAAO,MAAMG,eAAe;AAG5B,EAAC;;;;;;;;;;;;;;AAEuG,EAAC,uEAAA;AAEzG,UAAA,kDAA4D;AAC5D,MAAA,CAAO,MAAMK;IAAAA;IAAAA,SAAc,IAAIX,mBAAmB;YAChDY,QAAAA;YAAAA,GAAY;YAAA;wBACVC,IAAAA;oBAAAA,CAAMZ,UAAUa;oBAAAA,OAAQ;yBACxBC,MAAM;8BACNC,IAAAA,CAAAA,GAAU;wBAAA,QAAA;4BAAA,IAAA;4BAAA;yBAAA;;uBACV,2CAA2C;;iBAC3CC,YAAY;sBACZC,IAAAA,CAAAA;YAAAA,CAAU;SAAA;;SACVC,UAAU,EAAE;UACd,QAAA;YAAA,MAAA;gBACAC,OAAU,QAAA;wBAAA;4BACRC,KAAAA,CAAAA,GAAAA,IAAYnB,wMAAZmB,CAAAA,sBAAYnB,EAAAA,MAAAA,MAAAA,MAAAA,MAAAA,EAAAA,iBAAAA,CAAAA,CAAAA,EAAAA,6SAAAA,CAAAA,UAAAA,CAAAA,GAAAA,CAAAA,KAAAA,CAAAA,KAAAA,MAAAA,CAAAA,CAAAA,EAAAA,CAAAA,EAAAA,EAAAA;4BACd,OAAA,GAAA,6SAAA,CAAA,UAAA,CAAA,KAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BACA,MAAA,CAAA,YAAA,CAAA", "ignoreList": [0], "debugId": null}}]}