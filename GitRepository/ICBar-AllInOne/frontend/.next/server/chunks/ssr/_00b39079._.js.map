{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/components/layout/header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Header = registerClientReference(\n    function() { throw new Error(\"Attempted to call Head<PERSON>() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/header.tsx <module evaluation>\",\n    \"Header\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,kEACA", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/components/layout/header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Header = registerClientReference(\n    function() { throw new Error(\"Attempted to call Header() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/header.tsx\",\n    \"Header\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,8CACA", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/components/layout/footer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Footer = registerClientReference(\n    function() { throw new Error(\"Attempted to call Footer() from the server but Foot<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/footer.tsx <module evaluation>\",\n    \"Footer\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,kEACA", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/components/layout/footer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Footer = registerClientReference(\n    function() { throw new Error(\"Attempted to call Footer() from the server but Foot<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/footer.tsx\",\n    \"Footer\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,8CACA", "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 118, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-error text-error-foreground hover:bg-error/80\",\n        outline: \"text-foreground\",\n        success:\n          \"border-transparent bg-success text-success-foreground hover:bg-success/80\",\n        warning:\n          \"border-transparent bg-warning text-warning-foreground hover:bg-warning/80\",\n        info:\n          \"border-transparent bg-info text-info-foreground hover:bg-info/80\",\n      },\n      size: {\n        default: \"px-2.5 py-0.5 text-xs\",\n        sm: \"px-2 py-0.5 text-xs\",\n        lg: \"px-3 py-1 text-sm\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, size, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant, size }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;YACT,SACE;YACF,SACE;YACF,MACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,OAAmB;IAC/D,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;YAAS;QAAK,IAAI;QAAa,GAAG,KAAK;;;;;;AAE9E", "debugId": null}}, {"offset": {"line": 170, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/app/activities/page.tsx"], "sourcesContent": ["import { Header } from \"@/components/layout/header\"\nimport { Footer } from \"@/components/layout/footer\"\nimport { Container, Grid } from \"@/components/ui/layout\"\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Button } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Icons } from \"@/components/ui/icon\"\n\nexport default function ActivitiesPage() {\n  const activities = [\n    {\n      id: 1,\n      title: \"Notion Pro 年度会员限时免费\",\n      description: \"获取Notion Pro年度会员资格，享受无限制的团队协作功能\",\n      app: \"Notion\",\n      type: \"会员福利\",\n      status: \"进行中\",\n      endDate: \"2024-02-28\",\n      participants: 1234,\n      maxParticipants: 5000,\n      image: \"/api/placeholder/300/200\",\n      hot: true\n    },\n    {\n      id: 2,\n      title: \"Figma 专业版邀请码发放\",\n      description: \"限量发放Figma专业版邀请码，先到先得\",\n      app: \"Figma\",\n      type: \"邀请码\",\n      status: \"进行中\",\n      endDate: \"2024-02-25\",\n      participants: 856,\n      maxParticipants: 1000,\n      image: \"/api/placeholder/300/200\",\n      hot: true\n    },\n    {\n      id: 3,\n      title: \"Discord Nitro 免费体验\",\n      description: \"体验Discord Nitro的高级功能，包括高质量语音和自定义表情\",\n      app: \"Discord\",\n      type: \"免费试用\",\n      status: \"即将开始\",\n      endDate: \"2024-03-01\",\n      participants: 0,\n      maxParticipants: 2000,\n      image: \"/api/placeholder/300/200\",\n      hot: false\n    },\n    {\n      id: 4,\n      title: \"Spotify Premium 学生优惠\",\n      description: \"学生专享Spotify Premium优惠价格，享受无广告音乐体验\",\n      app: \"Spotify\",\n      type: \"学生优惠\",\n      status: \"进行中\",\n      endDate: \"2024-03-15\",\n      participants: 2341,\n      maxParticipants: 10000,\n      image: \"/api/placeholder/300/200\",\n      hot: false\n    }\n  ]\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case \"进行中\": return \"success\"\n      case \"即将开始\": return \"warning\"\n      case \"已结束\": return \"secondary\"\n      default: return \"secondary\"\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      <Header />\n      \n      <main className=\"py-8\">\n        <Container>\n          {/* 页面标题 */}\n          <div className=\"mb-8\">\n            <h1 className=\"text-3xl font-bold mb-2\">送码活动</h1>\n            <p className=\"text-muted-foreground\">\n              参与活动，获取专属邀请码和会员福利\n            </p>\n          </div>\n\n          {/* 活动统计 */}\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 mb-8\">\n            <Card>\n              <CardContent className=\"p-4 text-center\">\n                <div className=\"text-2xl font-bold text-primary mb-1\">12</div>\n                <div className=\"text-sm text-muted-foreground\">进行中活动</div>\n              </CardContent>\n            </Card>\n            <Card>\n              <CardContent className=\"p-4 text-center\">\n                <div className=\"text-2xl font-bold text-success mb-1\">4,567</div>\n                <div className=\"text-sm text-muted-foreground\">总参与人数</div>\n              </CardContent>\n            </Card>\n            <Card>\n              <CardContent className=\"p-4 text-center\">\n                <div className=\"text-2xl font-bold text-warning mb-1\">89%</div>\n                <div className=\"text-sm text-muted-foreground\">成功率</div>\n              </CardContent>\n            </Card>\n            <Card>\n              <CardContent className=\"p-4 text-center\">\n                <div className=\"text-2xl font-bold text-info mb-1\">156</div>\n                <div className=\"text-sm text-muted-foreground\">已发放码数</div>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* 筛选选项 */}\n          <div className=\"flex flex-wrap gap-2 mb-8\">\n            <Button variant=\"default\" size=\"sm\">全部活动</Button>\n            <Button variant=\"outline\" size=\"sm\">进行中</Button>\n            <Button variant=\"outline\" size=\"sm\">即将开始</Button>\n            <Button variant=\"outline\" size=\"sm\">会员福利</Button>\n            <Button variant=\"outline\" size=\"sm\">邀请码</Button>\n          </div>\n\n          {/* 活动列表 */}\n          <Grid cols={1} gap=\"md\" className=\"md:grid-cols-2\">\n            {activities.map((activity) => (\n              <Card key={activity.id} className=\"hover:shadow-lg transition-shadow\">\n                <div className=\"relative\">\n                  <img \n                    src={activity.image} \n                    alt={activity.title}\n                    className=\"w-full h-48 object-cover rounded-t-lg\"\n                  />\n                  {activity.hot && (\n                    <Badge className=\"absolute top-3 left-3\" variant=\"error\">\n                      🔥 热门\n                    </Badge>\n                  )}\n                  <Badge \n                    className=\"absolute top-3 right-3\" \n                    variant={getStatusColor(activity.status) as any}\n                  >\n                    {activity.status}\n                  </Badge>\n                </div>\n                \n                <CardHeader>\n                  <div className=\"flex items-start justify-between gap-4\">\n                    <div className=\"flex-1\">\n                      <CardTitle className=\"text-lg mb-2\">{activity.title}</CardTitle>\n                      <p className=\"text-sm text-muted-foreground mb-3\">\n                        {activity.description}\n                      </p>\n                      <div className=\"flex items-center gap-4 text-sm text-muted-foreground\">\n                        <div className=\"flex items-center gap-1\">\n                          <Icons.app className=\"w-4 h-4\" />\n                          <span>{activity.app}</span>\n                        </div>\n                        <div className=\"flex items-center gap-1\">\n                          <Icons.calendar className=\"w-4 h-4\" />\n                          <span>截止 {activity.endDate}</span>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </CardHeader>\n                \n                <CardContent className=\"pt-0\">\n                  <div className=\"mb-4\">\n                    <div className=\"flex justify-between text-sm mb-2\">\n                      <span>参与进度</span>\n                      <span>{activity.participants}/{activity.maxParticipants}</span>\n                    </div>\n                    <div className=\"w-full bg-muted rounded-full h-2\">\n                      <div \n                        className=\"bg-primary h-2 rounded-full transition-all duration-300\"\n                        style={{ \n                          width: `${(activity.participants / activity.maxParticipants) * 100}%` \n                        }}\n                      />\n                    </div>\n                  </div>\n                  \n                  <div className=\"flex gap-2\">\n                    <Button className=\"flex-1\" size=\"sm\">\n                      <Icons.gift className=\"w-4 h-4 mr-2\" />\n                      立即参与\n                    </Button>\n                    <Button variant=\"outline\" size=\"sm\">\n                      <Icons.share className=\"w-4 h-4\" />\n                    </Button>\n                  </div>\n                </CardContent>\n              </Card>\n            ))}\n          </Grid>\n\n          {/* 加载更多 */}\n          <div className=\"text-center mt-8\">\n            <Button variant=\"outline\">\n              查看更多活动\n            </Button>\n          </div>\n        </Container>\n      </main>\n\n      <Footer />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAEe,SAAS;IACtB,MAAM,aAAa;QACjB;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,KAAK;YACL,MAAM;YACN,QAAQ;YACR,SAAS;YACT,cAAc;YACd,iBAAiB;YACjB,OAAO;YACP,KAAK;QACP;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,KAAK;YACL,MAAM;YACN,QAAQ;YACR,SAAS;YACT,cAAc;YACd,iBAAiB;YACjB,OAAO;YACP,KAAK;QACP;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,KAAK;YACL,MAAM;YACN,QAAQ;YACR,SAAS;YACT,cAAc;YACd,iBAAiB;YACjB,OAAO;YACP,KAAK;QACP;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,KAAK;YACL,MAAM;YACN,QAAQ;YACR,SAAS;YACT,cAAc;YACd,iBAAiB;YACjB,OAAO;YACP,KAAK;QACP;KACD;IAED,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAO,OAAO;YACnB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,SAAM;;;;;0BAEP,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kIAAA,CAAA,YAAS;;sCAER,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0B;;;;;;8CACxC,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAMvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gIAAA,CAAA,OAAI;8CACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;0DAAuC;;;;;;0DACtD,8OAAC;gDAAI,WAAU;0DAAgC;;;;;;;;;;;;;;;;;8CAGnD,8OAAC,gIAAA,CAAA,OAAI;8CACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;0DAAuC;;;;;;0DACtD,8OAAC;gDAAI,WAAU;0DAAgC;;;;;;;;;;;;;;;;;8CAGnD,8OAAC,gIAAA,CAAA,OAAI;8CACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;0DAAuC;;;;;;0DACtD,8OAAC;gDAAI,WAAU;0DAAgC;;;;;;;;;;;;;;;;;8CAGnD,8OAAC,gIAAA,CAAA,OAAI;8CACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;0DAAoC;;;;;;0DACnD,8OAAC;gDAAI,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;;;;;;sCAMrD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;8CAAK;;;;;;8CACpC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;8CAAK;;;;;;8CACpC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;8CAAK;;;;;;8CACpC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;8CAAK;;;;;;8CACpC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;8CAAK;;;;;;;;;;;;sCAItC,8OAAC,kIAAA,CAAA,OAAI;4BAAC,MAAM;4BAAG,KAAI;4BAAK,WAAU;sCAC/B,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC,gIAAA,CAAA,OAAI;oCAAmB,WAAU;;sDAChC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,KAAK,SAAS,KAAK;oDACnB,KAAK,SAAS,KAAK;oDACnB,WAAU;;;;;;gDAEX,SAAS,GAAG,kBACX,8OAAC,iIAAA,CAAA,QAAK;oDAAC,WAAU;oDAAwB,SAAQ;8DAAQ;;;;;;8DAI3D,8OAAC,iIAAA,CAAA,QAAK;oDACJ,WAAU;oDACV,SAAS,eAAe,SAAS,MAAM;8DAEtC,SAAS,MAAM;;;;;;;;;;;;sDAIpB,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,gIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAgB,SAAS,KAAK;;;;;;sEACnD,8OAAC;4DAAE,WAAU;sEACV,SAAS,WAAW;;;;;;sEAEvB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,gIAAA,CAAA,QAAK,CAAC,GAAG;4EAAC,WAAU;;;;;;sFACrB,8OAAC;sFAAM,SAAS,GAAG;;;;;;;;;;;;8EAErB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,gIAAA,CAAA,QAAK,CAAC,QAAQ;4EAAC,WAAU;;;;;;sFAC1B,8OAAC;;gFAAK;gFAAI,SAAS,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAOpC,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAK;;;;;;8EACN,8OAAC;;wEAAM,SAAS,YAAY;wEAAC;wEAAE,SAAS,eAAe;;;;;;;;;;;;;sEAEzD,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEACC,WAAU;gEACV,OAAO;oEACL,OAAO,GAAG,AAAC,SAAS,YAAY,GAAG,SAAS,eAAe,GAAI,IAAI,CAAC,CAAC;gEACvE;;;;;;;;;;;;;;;;;8DAKN,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kIAAA,CAAA,SAAM;4DAAC,WAAU;4DAAS,MAAK;;8EAC9B,8OAAC,gIAAA,CAAA,QAAK,CAAC,IAAI;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGzC,8OAAC,kIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAU,MAAK;sEAC7B,cAAA,8OAAC,gIAAA,CAAA,QAAK,CAAC,KAAK;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;mCA/DpB,SAAS,EAAE;;;;;;;;;;sCAwE1B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;0CAAU;;;;;;;;;;;;;;;;;;;;;;0BAOhC,8OAAC,sIAAA,CAAA,SAAM;;;;;;;;;;;AAGb", "debugId": null}}, {"offset": {"line": 800, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 838, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAMhG,8BAA8B;AAG9B,OAAO,MAAMG,eAAe;AAG5B,EAAC;;;;;;;;;;;;;;AAEuG,EAAC,uEAAA;AAEzG,UAAA,kDAA4D;AAC5D,MAAA,CAAO,MAAMK;IAAAA;IAAAA,SAAc,IAAIX,mBAAmB;YAChDY,QAAAA;YAAAA,GAAY;YAAA;wBACVC,IAAAA;oBAAAA,CAAMZ,UAAUa;oBAAAA,OAAQ;yBACxBC,MAAM;8BACNC,IAAAA,CAAAA,GAAU;wBAAA,QAAA;4BAAA,IAAA;4BAAA;yBAAA;;uBACV,2CAA2C;;iBAC3CC,YAAY;sBACZC,IAAAA,CAAAA;YAAAA,CAAU;SAAA;;SACVC,UAAU,EAAE;UACd,QAAA;YAAA,MAAA;gBACAC,OAAU,QAAA;wBAAA;4BACRC,KAAAA,CAAAA,GAAAA,IAAYnB,wMAAZmB,CAAAA,sBAAYnB,EAAAA,MAAAA,MAAAA,MAAAA,MAAAA,EAAAA,iBAAAA,CAAAA,CAAAA,EAAAA,6SAAAA,CAAAA,UAAAA,CAAAA,GAAAA,CAAAA,KAAAA,CAAAA,KAAAA,MAAAA,CAAAA,CAAAA,EAAAA,CAAAA,EAAAA,EAAAA;4BACd,OAAA,GAAA,6SAAA,CAAA,UAAA,CAAA,KAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BACA,MAAA,CAAA,YAAA,CAAA", "ignoreList": [0], "debugId": null}}]}