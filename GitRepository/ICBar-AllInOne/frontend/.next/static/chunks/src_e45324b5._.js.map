{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/utils/index.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\n// 合并CSS类名\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// 格式化日期\nexport function formatDate(date: string | Date): string {\n  const d = new Date(date);\n  return d.toLocaleDateString('zh-CN', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  });\n}\n\n// 格式化相对时间\nexport function formatRelativeTime(date: string | Date): string {\n  const now = new Date();\n  const target = new Date(date);\n  const diffInSeconds = Math.floor((now.getTime() - target.getTime()) / 1000);\n\n  if (diffInSeconds < 60) {\n    return '刚刚';\n  } else if (diffInSeconds < 3600) {\n    const minutes = Math.floor(diffInSeconds / 60);\n    return `${minutes}分钟前`;\n  } else if (diffInSeconds < 86400) {\n    const hours = Math.floor(diffInSeconds / 3600);\n    return `${hours}小时前`;\n  } else if (diffInSeconds < 2592000) {\n    const days = Math.floor(diffInSeconds / 86400);\n    return `${days}天前`;\n  } else {\n    return formatDate(date);\n  }\n}\n\n// 生成随机字符串\nexport function generateRandomString(length: number): string {\n  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n  let result = '';\n  for (let i = 0; i < length; i++) {\n    result += chars.charAt(Math.floor(Math.random() * chars.length));\n  }\n  return result;\n}\n\n// 验证邮箱格式\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\n// 截断文本\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength) + '...';\n}\n\n// 应用分类映射\nexport const categoryMap: Record<string, string> = {\n  productivity: '效率工具',\n  entertainment: '娱乐',\n  education: '教育',\n  business: '商务',\n  lifestyle: '生活方式',\n  utilities: '实用工具',\n  games: '游戏',\n  social: '社交',\n  other: '其他'\n};\n\n// 获取分类显示名称\nexport function getCategoryDisplayName(category: string): string {\n  return categoryMap[category] || category;\n}\n\n// 验证URL格式\nexport function isValidUrl(url: string): boolean {\n  try {\n    new URL(url);\n    return true;\n  } catch {\n    return false;\n  }\n}\n\n// 计算活动热度分数\nexport function calculateActivityScore(\n  activityCount: number,\n  totalCodes: number,\n  createdAt: string\n): number {\n  const daysSinceCreation = Math.floor(\n    (Date.now() - new Date(createdAt).getTime()) / (1000 * 60 * 60 * 24)\n  );\n  \n  // 基础分数：活动数量 * 2 + 总邀请码数量\n  const baseScore = activityCount * 2 + totalCodes;\n  \n  // 时间衰减因子（越新的应用分数越高）\n  const timeDecay = Math.max(0.1, 1 - daysSinceCreation / 365);\n  \n  return Math.round(baseScore * timeDecay);\n}\n\n// 防抖函数\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n// 节流函数\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n}\n\n// 格式化数字为可读格式\nexport function formatNumber(num: number): string {\n  if (num >= 1000000) {\n    return (num / 1000000).toFixed(1) + 'M'\n  }\n  if (num >= 1000) {\n    return (num / 1000).toFixed(1) + 'K'\n  }\n  return num.toString()\n}\n\n// 深拷贝对象\nexport function deepClone<T>(obj: T): T {\n  if (obj === null || typeof obj !== 'object') return obj\n  if (obj instanceof Date) return new Date(obj.getTime()) as T\n  if (obj instanceof Array) return obj.map(item => deepClone(item)) as T\n  if (typeof obj === 'object') {\n    const clonedObj = {} as T\n    for (const key in obj) {\n      if (obj.hasOwnProperty(key)) {\n        clonedObj[key] = deepClone(obj[key])\n      }\n    }\n    return clonedObj\n  }\n  return obj\n}\n\n// 检查是否为移动设备\nexport function isMobile(): boolean {\n  if (typeof window === 'undefined') return false\n  return window.innerWidth < 768\n}\n\n// 平滑滚动到元素\nexport function scrollToElement(elementId: string, offset: number = 0): void {\n  const element = document.getElementById(elementId)\n  if (element) {\n    const top = element.offsetTop - offset\n    window.scrollTo({\n      top,\n      behavior: 'smooth'\n    })\n  }\n}\n\n// 复制文本到剪贴板\nexport async function copyToClipboard(text: string): Promise<boolean> {\n  try {\n    await navigator.clipboard.writeText(text)\n    return true\n  } catch (err) {\n    // 降级方案\n    const textArea = document.createElement('textarea')\n    textArea.value = text\n    document.body.appendChild(textArea)\n    textArea.select()\n    const success = document.execCommand('copy')\n    document.body.removeChild(textArea)\n    return success\n  }\n}\n\n// 获取文件大小的可读格式\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes'\n\n  const k = 1024\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n\n// 获取颜色的对比色\nexport function getContrastColor(hexColor: string): string {\n  // 移除 # 符号\n  const hex = hexColor.replace('#', '')\n\n  // 转换为 RGB\n  const r = parseInt(hex.substr(0, 2), 16)\n  const g = parseInt(hex.substr(2, 2), 16)\n  const b = parseInt(hex.substr(4, 2), 16)\n\n  // 计算亮度\n  const brightness = (r * 299 + g * 587 + b * 114) / 1000\n\n  // 返回对比色\n  return brightness > 128 ? '#000000' : '#ffffff'\n}\n\n// 生成随机ID\nexport function generateId(): string {\n  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)\n}\n\n// 格式化价格\nexport function formatPrice(price: number, currency: string = '¥'): string {\n  return `${currency}${price.toFixed(2)}`\n}\n\n// 获取设备类型\nexport function getDeviceType(): 'mobile' | 'tablet' | 'desktop' {\n  if (typeof window === 'undefined') return 'desktop'\n\n  const width = window.innerWidth\n  if (width < 768) return 'mobile'\n  if (width < 1024) return 'tablet'\n  return 'desktop'\n}\n\n// 检查是否支持WebP\nexport function supportsWebP(): Promise<boolean> {\n  return new Promise((resolve) => {\n    const webP = new Image()\n    webP.onload = webP.onerror = () => {\n      resolve(webP.height === 2)\n    }\n    webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA'\n  })\n}\n\n// 本地存储工具\nexport const storage = {\n  get: <T>(key: string, defaultValue?: T): T | null => {\n    if (typeof window === 'undefined') return defaultValue || null\n    try {\n      const item = localStorage.getItem(key)\n      return item ? JSON.parse(item) : defaultValue || null\n    } catch {\n      return defaultValue || null\n    }\n  },\n\n  set: <T>(key: string, value: T): void => {\n    if (typeof window === 'undefined') return\n    try {\n      localStorage.setItem(key, JSON.stringify(value))\n    } catch (error) {\n      console.error('Failed to save to localStorage:', error)\n    }\n  },\n\n  remove: (key: string): void => {\n    if (typeof window === 'undefined') return\n    localStorage.removeItem(key)\n  },\n\n  clear: (): void => {\n    if (typeof window === 'undefined') return\n    localStorage.clear()\n  }\n}\n\n// 生成头像的工具函数\nexport function generateAvatar(name: string, size: number = 64, colors?: string[]): string {\n  const defaultColors = [\n    '#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6',\n    '#06b6d4', '#84cc16', '#f97316', '#ec4899', '#6366f1'\n  ]\n\n  const colorPalette = colors || defaultColors\n  const initials = name\n    .split(' ')\n    .map(word => word[0])\n    .join('')\n    .toUpperCase()\n    .slice(0, 2)\n\n  // 根据名字生成一致的颜色\n  const colorIndex = name.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0) % colorPalette.length\n  const color = colorPalette[colorIndex]\n\n  const svg = `\n    <svg width=\"${size}\" height=\"${size}\" xmlns=\"http://www.w3.org/2000/svg\">\n      <defs>\n        <linearGradient id=\"grad-${name.replace(/\\s+/g, '')}\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n          <stop offset=\"0%\" style=\"stop-color:${color};stop-opacity:1\" />\n          <stop offset=\"100%\" style=\"stop-color:${color}dd;stop-opacity:1\" />\n        </linearGradient>\n      </defs>\n      <circle cx=\"${size/2}\" cy=\"${size/2}\" r=\"${size/2}\" fill=\"url(#grad-${name.replace(/\\s+/g, '')})\"/>\n      <text x=\"50%\" y=\"50%\" text-anchor=\"middle\" dy=\"0.3em\" fill=\"white\" font-family=\"system-ui, sans-serif\" font-size=\"${size * 0.3}\" font-weight=\"600\">\n        ${initials}\n      </text>\n    </svg>\n  `.trim()\n\n  return `data:image/svg+xml,${encodeURIComponent(svg)}`\n}\n\n// 生成应用图标的工具函数\nexport function generateAppIcon(name: string, color?: string, size: number = 64): string {\n  const defaultColor = '#3b82f6'\n  const iconColor = color || defaultColor\n  const initials = name\n    .split(' ')\n    .map(word => word[0])\n    .join('')\n    .toUpperCase()\n    .slice(0, 2)\n\n  const svg = `\n    <svg width=\"${size}\" height=\"${size}\" xmlns=\"http://www.w3.org/2000/svg\">\n      <defs>\n        <linearGradient id=\"app-grad-${name.replace(/\\s+/g, '')}\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n          <stop offset=\"0%\" style=\"stop-color:${iconColor};stop-opacity:1\" />\n          <stop offset=\"100%\" style=\"stop-color:${iconColor}dd;stop-opacity:1\" />\n        </linearGradient>\n      </defs>\n      <rect width=\"100%\" height=\"100%\" fill=\"url(#app-grad-${name.replace(/\\s+/g, '')})\" rx=\"${size * 0.2}\"/>\n      <text x=\"50%\" y=\"50%\" text-anchor=\"middle\" dy=\"0.3em\" fill=\"white\" font-family=\"system-ui, sans-serif\" font-size=\"${size * 0.3}\" font-weight=\"600\">\n        ${initials}\n      </text>\n    </svg>\n  `.trim()\n\n  return `data:image/svg+xml,${encodeURIComponent(svg)}`\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAGO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAGO,SAAS,mBAAmB,IAAmB;IACpD,MAAM,MAAM,IAAI;IAChB,MAAM,SAAS,IAAI,KAAK;IACxB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,OAAO,OAAO,EAAE,IAAI;IAEtE,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT,OAAO,IAAI,gBAAgB,MAAM;QAC/B,MAAM,UAAU,KAAK,KAAK,CAAC,gBAAgB;QAC3C,OAAO,GAAG,QAAQ,GAAG,CAAC;IACxB,OAAO,IAAI,gBAAgB,OAAO;QAChC,MAAM,QAAQ,KAAK,KAAK,CAAC,gBAAgB;QACzC,OAAO,GAAG,MAAM,GAAG,CAAC;IACtB,OAAO,IAAI,gBAAgB,SAAS;QAClC,MAAM,OAAO,KAAK,KAAK,CAAC,gBAAgB;QACxC,OAAO,GAAG,KAAK,EAAE,CAAC;IACpB,OAAO;QACL,OAAO,WAAW;IACpB;AACF;AAGO,SAAS,qBAAqB,MAAc;IACjD,MAAM,QAAQ;IACd,IAAI,SAAS;IACb,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,UAAU,MAAM,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM;IAChE;IACA,OAAO;AACT;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAGO,MAAM,cAAsC;IACjD,cAAc;IACd,eAAe;IACf,WAAW;IACX,UAAU;IACV,WAAW;IACX,WAAW;IACX,OAAO;IACP,QAAQ;IACR,OAAO;AACT;AAGO,SAAS,uBAAuB,QAAgB;IACrD,OAAO,WAAW,CAAC,SAAS,IAAI;AAClC;AAGO,SAAS,WAAW,GAAW;IACpC,IAAI;QACF,IAAI,IAAI;QACR,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAGO,SAAS,uBACd,aAAqB,EACrB,UAAkB,EAClB,SAAiB;IAEjB,MAAM,oBAAoB,KAAK,KAAK,CAClC,CAAC,KAAK,GAAG,KAAK,IAAI,KAAK,WAAW,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;IAGrE,yBAAyB;IACzB,MAAM,YAAY,gBAAgB,IAAI;IAEtC,oBAAoB;IACpB,MAAM,YAAY,KAAK,GAAG,CAAC,KAAK,IAAI,oBAAoB;IAExD,OAAO,KAAK,KAAK,CAAC,YAAY;AAChC;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF;AAGO,SAAS,aAAa,GAAW;IACtC,IAAI,OAAO,SAAS;QAClB,OAAO,CAAC,MAAM,OAAO,EAAE,OAAO,CAAC,KAAK;IACtC;IACA,IAAI,OAAO,MAAM;QACf,OAAO,CAAC,MAAM,IAAI,EAAE,OAAO,CAAC,KAAK;IACnC;IACA,OAAO,IAAI,QAAQ;AACrB;AAGO,SAAS,UAAa,GAAM;IACjC,IAAI,QAAQ,QAAQ,OAAO,QAAQ,UAAU,OAAO;IACpD,IAAI,eAAe,MAAM,OAAO,IAAI,KAAK,IAAI,OAAO;IACpD,IAAI,eAAe,OAAO,OAAO,IAAI,GAAG,CAAC,CAAA,OAAQ,UAAU;IAC3D,IAAI,OAAO,QAAQ,UAAU;QAC3B,MAAM,YAAY,CAAC;QACnB,IAAK,MAAM,OAAO,IAAK;YACrB,IAAI,IAAI,cAAc,CAAC,MAAM;gBAC3B,SAAS,CAAC,IAAI,GAAG,UAAU,GAAG,CAAC,IAAI;YACrC;QACF;QACA,OAAO;IACT;IACA,OAAO;AACT;AAGO,SAAS;IACd,uCAAmC;;IAAW;IAC9C,OAAO,OAAO,UAAU,GAAG;AAC7B;AAGO,SAAS,gBAAgB,SAAiB,EAAE,SAAiB,CAAC;IACnE,MAAM,UAAU,SAAS,cAAc,CAAC;IACxC,IAAI,SAAS;QACX,MAAM,MAAM,QAAQ,SAAS,GAAG;QAChC,OAAO,QAAQ,CAAC;YACd;YACA,UAAU;QACZ;IACF;AACF;AAGO,eAAe,gBAAgB,IAAY;IAChD,IAAI;QACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;QACpC,OAAO;IACT,EAAE,OAAO,KAAK;QACZ,OAAO;QACP,MAAM,WAAW,SAAS,aAAa,CAAC;QACxC,SAAS,KAAK,GAAG;QACjB,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,SAAS,MAAM;QACf,MAAM,UAAU,SAAS,WAAW,CAAC;QACrC,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,OAAO;IACT;AACF;AAGO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;KAAK;IAC/C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAGO,SAAS,iBAAiB,QAAgB;IAC/C,UAAU;IACV,MAAM,MAAM,SAAS,OAAO,CAAC,KAAK;IAElC,UAAU;IACV,MAAM,IAAI,SAAS,IAAI,MAAM,CAAC,GAAG,IAAI;IACrC,MAAM,IAAI,SAAS,IAAI,MAAM,CAAC,GAAG,IAAI;IACrC,MAAM,IAAI,SAAS,IAAI,MAAM,CAAC,GAAG,IAAI;IAErC,OAAO;IACP,MAAM,aAAa,CAAC,IAAI,MAAM,IAAI,MAAM,IAAI,GAAG,IAAI;IAEnD,QAAQ;IACR,OAAO,aAAa,MAAM,YAAY;AACxC;AAGO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;AAC/F;AAGO,SAAS,YAAY,KAAa,EAAE,WAAmB,GAAG;IAC/D,OAAO,GAAG,WAAW,MAAM,OAAO,CAAC,IAAI;AACzC;AAGO,SAAS;IACd,uCAAmC;;IAAe;IAElD,MAAM,QAAQ,OAAO,UAAU;IAC/B,IAAI,QAAQ,KAAK,OAAO;IACxB,IAAI,QAAQ,MAAM,OAAO;IACzB,OAAO;AACT;AAGO,SAAS;IACd,OAAO,IAAI,QAAQ,CAAC;QAClB,MAAM,OAAO,IAAI;QACjB,KAAK,MAAM,GAAG,KAAK,OAAO,GAAG;YAC3B,QAAQ,KAAK,MAAM,KAAK;QAC1B;QACA,KAAK,GAAG,GAAG;IACb;AACF;AAGO,MAAM,UAAU;IACrB,KAAK,CAAI,KAAa;QACpB,uCAAmC;;QAA0B;QAC7D,IAAI;YACF,MAAM,OAAO,aAAa,OAAO,CAAC;YAClC,OAAO,OAAO,KAAK,KAAK,CAAC,QAAQ,gBAAgB;QACnD,EAAE,OAAM;YACN,OAAO,gBAAgB;QACzB;IACF;IAEA,KAAK,CAAI,KAAa;QACpB,uCAAmC;;QAAK;QACxC,IAAI;YACF,aAAa,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD;IACF;IAEA,QAAQ,CAAC;QACP,uCAAmC;;QAAK;QACxC,aAAa,UAAU,CAAC;IAC1B;IAEA,OAAO;QACL,uCAAmC;;QAAK;QACxC,aAAa,KAAK;IACpB;AACF;AAGO,SAAS,eAAe,IAAY,EAAE,OAAe,EAAE,EAAE,MAAiB;IAC/E,MAAM,gBAAgB;QACpB;QAAW;QAAW;QAAW;QAAW;QAC5C;QAAW;QAAW;QAAW;QAAW;KAC7C;IAED,MAAM,eAAe,UAAU;IAC/B,MAAM,WAAW,KACd,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,EAAE,EACnB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IAEZ,cAAc;IACd,MAAM,aAAa,KAAK,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,UAAU,CAAC,IAAI,KAAK,aAAa,MAAM;IAC1G,MAAM,QAAQ,YAAY,CAAC,WAAW;IAEtC,MAAM,MAAM,CAAC;gBACC,EAAE,KAAK,UAAU,EAAE,KAAK;;iCAEP,EAAE,KAAK,OAAO,CAAC,QAAQ,IAAI;8CACd,EAAE,MAAM;gDACN,EAAE,MAAM;;;kBAGtC,EAAE,OAAK,EAAE,MAAM,EAAE,OAAK,EAAE,KAAK,EAAE,OAAK,EAAE,kBAAkB,EAAE,KAAK,OAAO,CAAC,QAAQ,IAAI;wHACmB,EAAE,OAAO,IAAI;QAC7H,EAAE,SAAS;;;EAGjB,CAAC,CAAC,IAAI;IAEN,OAAO,CAAC,mBAAmB,EAAE,mBAAmB,MAAM;AACxD;AAGO,SAAS,gBAAgB,IAAY,EAAE,KAAc,EAAE,OAAe,EAAE;IAC7E,MAAM,eAAe;IACrB,MAAM,YAAY,SAAS;IAC3B,MAAM,WAAW,KACd,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,EAAE,EACnB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IAEZ,MAAM,MAAM,CAAC;gBACC,EAAE,KAAK,UAAU,EAAE,KAAK;;qCAEH,EAAE,KAAK,OAAO,CAAC,QAAQ,IAAI;8CAClB,EAAE,UAAU;gDACV,EAAE,UAAU;;;2DAGD,EAAE,KAAK,OAAO,CAAC,QAAQ,IAAI,OAAO,EAAE,OAAO,IAAI;wHACc,EAAE,OAAO,IAAI;QAC7H,EAAE,SAAS;;;EAGjB,CAAC,CAAC,IAAI;IAEN,OAAO,CAAC,mBAAmB,EAAE,mBAAmB,MAAM;AACxD", "debugId": null}}, {"offset": {"line": 334, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90 hover:shadow-md active:scale-95\",\n        destructive:\n          \"bg-error text-error-foreground shadow-sm hover:bg-error/90 hover:shadow-md active:scale-95\",\n        outline:\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground hover:shadow-md active:scale-95\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80 hover:shadow-md active:scale-95\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground active:scale-95\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n        success: \"bg-success text-success-foreground shadow hover:bg-success/90 hover:shadow-md active:scale-95\",\n        warning: \"bg-warning text-warning-foreground shadow hover:bg-warning/90 hover:shadow-md active:scale-95\",\n        info: \"bg-info text-info-foreground shadow hover:bg-info/90 hover:shadow-md active:scale-95\",\n        gradient: \"bg-gradient-to-r from-primary to-primary/80 text-primary-foreground shadow-lg hover:shadow-xl hover:scale-105 active:scale-95\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-md px-3 text-xs\",\n        lg: \"h-10 rounded-md px-8\",\n        xl: \"h-12 rounded-lg px-10 text-base\",\n        icon: \"h-9 w-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n  loading?: boolean\n  leftIcon?: React.ReactNode\n  rightIcon?: React.ReactNode\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({\n    className,\n    variant,\n    size,\n    asChild = false,\n    loading = false,\n    disabled,\n    children,\n    leftIcon,\n    rightIcon,\n    ...props\n  }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {leftIcon && !loading && leftIcon}\n        {children}\n        {rightIcon && !loading && rightIcon}\n      </Comp>\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+UACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;YACN,SAAS;YACT,SAAS;YACT,MAAM;YACN,UAAU;QACZ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAYF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EACC,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,UAAU,KAAK,EACf,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,GAAG,OACJ,EAAE;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,6LAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP,YAAY,CAAC,WAAW;YACxB;YACA,aAAa,CAAC,WAAW;;;;;;;AAGhC;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 445, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/components/ui/layout.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/utils\"\n\n// 容器组件\nconst containerVariants = cva(\n  \"mx-auto w-full\",\n  {\n    variants: {\n      size: {\n        sm: \"max-w-screen-sm\",\n        md: \"max-w-screen-md\",\n        lg: \"max-w-screen-lg\",\n        xl: \"max-w-screen-xl\",\n        \"2xl\": \"max-w-screen-2xl\",\n        full: \"max-w-full\",\n      },\n      padding: {\n        none: \"px-0\",\n        sm: \"px-4\",\n        md: \"px-6\",\n        lg: \"px-8\",\n      },\n    },\n    defaultVariants: {\n      size: \"xl\",\n      padding: \"md\",\n    },\n  }\n)\n\nexport interface ContainerProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof containerVariants> {}\n\nconst Container = React.forwardRef<HTMLDivElement, ContainerProps>(\n  ({ className, size, padding, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(containerVariants({ size, padding, className }))}\n      {...props}\n    />\n  )\n)\nContainer.displayName = \"Container\"\n\n// 网格系统\nconst gridVariants = cva(\n  \"grid\",\n  {\n    variants: {\n      cols: {\n        1: \"grid-cols-1\",\n        2: \"grid-cols-1 md:grid-cols-2\",\n        3: \"grid-cols-1 md:grid-cols-2 lg:grid-cols-3\",\n        4: \"grid-cols-1 md:grid-cols-2 lg:grid-cols-4\",\n        5: \"grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5\",\n        6: \"grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6\",\n        12: \"grid-cols-12\",\n      },\n      gap: {\n        none: \"gap-0\",\n        sm: \"gap-2\",\n        md: \"gap-4\",\n        lg: \"gap-6\",\n        xl: \"gap-8\",\n      },\n    },\n    defaultVariants: {\n      cols: 1,\n      gap: \"md\",\n    },\n  }\n)\n\nexport interface GridProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof gridVariants> {}\n\nconst Grid = React.forwardRef<HTMLDivElement, GridProps>(\n  ({ className, cols, gap, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(gridVariants({ cols, gap, className }))}\n      {...props}\n    />\n  )\n)\nGrid.displayName = \"Grid\"\n\n// Flex布局组件\nconst flexVariants = cva(\n  \"flex\",\n  {\n    variants: {\n      direction: {\n        row: \"flex-row\",\n        col: \"flex-col\",\n        \"row-reverse\": \"flex-row-reverse\",\n        \"col-reverse\": \"flex-col-reverse\",\n      },\n      align: {\n        start: \"items-start\",\n        center: \"items-center\",\n        end: \"items-end\",\n        stretch: \"items-stretch\",\n        baseline: \"items-baseline\",\n      },\n      justify: {\n        start: \"justify-start\",\n        center: \"justify-center\",\n        end: \"justify-end\",\n        between: \"justify-between\",\n        around: \"justify-around\",\n        evenly: \"justify-evenly\",\n      },\n      wrap: {\n        nowrap: \"flex-nowrap\",\n        wrap: \"flex-wrap\",\n        \"wrap-reverse\": \"flex-wrap-reverse\",\n      },\n      gap: {\n        none: \"gap-0\",\n        sm: \"gap-2\",\n        md: \"gap-4\",\n        lg: \"gap-6\",\n        xl: \"gap-8\",\n      },\n    },\n    defaultVariants: {\n      direction: \"row\",\n      align: \"center\",\n      justify: \"start\",\n      wrap: \"nowrap\",\n      gap: \"none\",\n    },\n  }\n)\n\nexport interface FlexProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof flexVariants> {}\n\nconst Flex = React.forwardRef<HTMLDivElement, FlexProps>(\n  ({ className, direction, align, justify, wrap, gap, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(flexVariants({ direction, align, justify, wrap, gap, className }))}\n      {...props}\n    />\n  )\n)\nFlex.displayName = \"Flex\"\n\n// 堆栈组件\nconst stackVariants = cva(\n  \"flex\",\n  {\n    variants: {\n      direction: {\n        vertical: \"flex-col\",\n        horizontal: \"flex-row\",\n      },\n      spacing: {\n        none: \"gap-0\",\n        xs: \"gap-1\",\n        sm: \"gap-2\",\n        md: \"gap-4\",\n        lg: \"gap-6\",\n        xl: \"gap-8\",\n      },\n      align: {\n        start: \"items-start\",\n        center: \"items-center\",\n        end: \"items-end\",\n        stretch: \"items-stretch\",\n      },\n    },\n    defaultVariants: {\n      direction: \"vertical\",\n      spacing: \"md\",\n      align: \"stretch\",\n    },\n  }\n)\n\nexport interface StackProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof stackVariants> {}\n\nconst Stack = React.forwardRef<HTMLDivElement, StackProps>(\n  ({ className, direction, spacing, align, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(stackVariants({ direction, spacing, align, className }))}\n      {...props}\n    />\n  )\n)\nStack.displayName = \"Stack\"\n\n// 分隔符组件\nconst Separator = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & {\n    orientation?: \"horizontal\" | \"vertical\"\n  }\n>(({ className, orientation = \"horizontal\", ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"shrink-0 bg-border\",\n      orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\",\n      className\n    )}\n    {...props}\n  />\n))\nSeparator.displayName = \"Separator\"\n\n// 间距组件\nconst Spacer: React.FC<{ size?: \"sm\" | \"md\" | \"lg\" | \"xl\" }> = ({ size = \"md\" }) => {\n  const sizeClasses = {\n    sm: \"h-2\",\n    md: \"h-4\",\n    lg: \"h-6\",\n    xl: \"h-8\",\n  }\n  \n  return <div className={sizeClasses[size]} />\n}\n\n// 居中组件\nconst Center = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center justify-center\", className)}\n    {...props}\n  />\n))\nCenter.displayName = \"Center\"\n\n// 响应式显示组件\nconst Show: React.FC<{\n  when: boolean\n  fallback?: React.ReactNode\n  children: React.ReactNode\n}> = ({ when, fallback = null, children }) => {\n  return when ? <>{children}</> : <>{fallback}</>\n}\n\n// 断点显示组件\nconst Breakpoint: React.FC<{\n  show?: (\"sm\" | \"md\" | \"lg\" | \"xl\" | \"2xl\")[]\n  hide?: (\"sm\" | \"md\" | \"lg\" | \"xl\" | \"2xl\")[]\n  children: React.ReactNode\n}> = ({ show, hide, children }) => {\n  const showClasses = show?.map(bp => `${bp}:block`).join(\" \") || \"\"\n  const hideClasses = hide?.map(bp => `${bp}:hidden`).join(\" \") || \"\"\n  \n  return (\n    <div className={cn(\"hidden\", showClasses, hideClasses)}>\n      {children}\n    </div>\n  )\n}\n\nexport {\n  Container,\n  Grid,\n  Flex,\n  Stack,\n  Separator,\n  Spacer,\n  Center,\n  Show,\n  Breakpoint,\n  containerVariants,\n  gridVariants,\n  flexVariants,\n  stackVariants,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AACA;;;;;AAEA,OAAO;AACP,MAAM,oBAAoB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EAC1B,kBACA;IACE,UAAU;QACR,MAAM;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,OAAO;YACP,MAAM;QACR;QACA,SAAS;YACP,MAAM;YACN,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,MAAM;QACN,SAAS;IACX;AACF;AAOF,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC/B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBACvC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;YAAE;YAAM;YAAS;QAAU;QAC1D,GAAG,KAAK;;;;;;;AAIf,UAAU,WAAW,GAAG;AAExB,OAAO;AACP,MAAM,eAAe,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACrB,QACA;IACE,UAAU;QACR,MAAM;YACJ,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG;YACH,IAAI;QACN;QACA,KAAK;YACH,MAAM;YACN,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,MAAM;QACN,KAAK;IACP;AACF;AAOF,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAC1B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,OAAO,EAAE,oBACnC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAAE;YAAM;YAAK;QAAU;QACjD,GAAG,KAAK;;;;;;;AAIf,KAAK,WAAW,GAAG;AAEnB,WAAW;AACX,MAAM,eAAe,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACrB,QACA;IACE,UAAU;QACR,WAAW;YACT,KAAK;YACL,KAAK;YACL,eAAe;YACf,eAAe;QACjB;QACA,OAAO;YACL,OAAO;YACP,QAAQ;YACR,KAAK;YACL,SAAS;YACT,UAAU;QACZ;QACA,SAAS;YACP,OAAO;YACP,QAAQ;YACR,KAAK;YACL,SAAS;YACT,QAAQ;YACR,QAAQ;QACV;QACA,MAAM;YACJ,QAAQ;YACR,MAAM;YACN,gBAAgB;QAClB;QACA,KAAK;YACH,MAAM;YACN,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,WAAW;QACX,OAAO;QACP,SAAS;QACT,MAAM;QACN,KAAK;IACP;AACF;AAOF,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAC1B,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,OAAO,EAAE,oBAC9D,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAAE;YAAW;YAAO;YAAS;YAAM;YAAK;QAAU;QAC5E,GAAG,KAAK;;;;;;;AAIf,KAAK,WAAW,GAAG;AAEnB,OAAO;AACP,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,QACA;IACE,UAAU;QACR,WAAW;YACT,UAAU;YACV,YAAY;QACd;QACA,SAAS;YACP,MAAM;YACN,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,OAAO;YACL,OAAO;YACP,QAAQ;YACR,KAAK;YACL,SAAS;QACX;IACF;IACA,iBAAiB;QACf,WAAW;QACX,SAAS;QACT,OAAO;IACT;AACF;AAOF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAC3B,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACnD,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;YAAW;YAAS;YAAO;QAAU;QAClE,GAAG,KAAK;;;;;;;AAIf,MAAM,WAAW,GAAG;AAEpB,QAAQ;AACR,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAK/B,CAAC,EAAE,SAAS,EAAE,cAAc,YAAY,EAAE,GAAG,OAAO,EAAE,oBACtD,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EACV,sBACA,gBAAgB,eAAe,mBAAmB,kBAClD;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,OAAO;AACP,MAAM,SAAyD,CAAC,EAAE,OAAO,IAAI,EAAE;IAC7E,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBAAO,6LAAC;QAAI,WAAW,WAAW,CAAC,KAAK;;;;;;AAC1C;OATM;AAWN,OAAO;AACP,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QACjD,GAAG,KAAK;;;;;;;AAGb,OAAO,WAAW,GAAG;AAErB,UAAU;AACV,MAAM,OAID,CAAC,EAAE,IAAI,EAAE,WAAW,IAAI,EAAE,QAAQ,EAAE;IACvC,OAAO,qBAAO;kBAAG;sCAAe;kBAAG;;AACrC;OANM;AAQN,SAAS;AACT,MAAM,aAID,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE;IAC5B,MAAM,cAAc,MAAM,IAAI,CAAA,KAAM,GAAG,GAAG,MAAM,CAAC,EAAE,KAAK,QAAQ;IAChE,MAAM,cAAc,MAAM,IAAI,CAAA,KAAM,GAAG,GAAG,OAAO,CAAC,EAAE,KAAK,QAAQ;IAEjE,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,UAAU,aAAa;kBACvC;;;;;;AAGP;OAbM", "debugId": null}}, {"offset": {"line": 743, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/components/ui/icon.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/utils\"\nimport {\n  // 导航图标\n  Home,\n  Search,\n  User,\n  Settings,\n  Menu,\n  X,\n  ChevronDown,\n  ChevronUp,\n  ChevronLeft,\n  ChevronRight,\n  ArrowLeft,\n  ArrowRight,\n  ArrowUpDown,\n  \n  // 功能图标\n  Download,\n  Upload,\n  Share,\n  Heart,\n  Star,\n  Bookmark,\n  Eye,\n  EyeOff,\n  Edit,\n  Trash2,\n  Copy,\n  Check,\n  Plus,\n  Minus,\n  \n  // 状态图标\n  CheckCircle,\n  AlertCircle,\n  Info,\n  AlertTriangle,\n  Clock,\n  Calendar,\n  MapPin,\n  Phone,\n  Mail,\n  Globe,\n  \n  // 媒体图标\n  Play,\n  Pause,\n  Volume2,\n  VolumeX,\n  Image,\n  Video,\n  Music,\n  File,\n  FileText,\n  \n  // 社交图标\n  MessageCircle,\n  Send,\n  ThumbsUp,\n  ThumbsDown,\n  Users,\n  UserPlus,\n  \n  // 商业图标\n  ShoppingCart,\n  CreditCard,\n  DollarSign,\n  TrendingUp,\n  TrendingDown,\n  BarChart3,\n  PieChart,\n  \n  // 系统图标\n  Loader2,\n  RefreshCw,\n  Power,\n  Wifi,\n  WifiOff,\n  Battery,\n  Signal,\n  Shield,\n  Bell,\n  Flag,\n  Gift,\n  Lock,\n  Key,\n  Lightbulb,\n  Camera,\n  Zap,\n  Crown,\n  Github,\n  Filter,\n\n  type LucideIcon,\n} from \"lucide-react\"\n\nconst iconVariants = cva(\n  \"inline-flex items-center justify-center\",\n  {\n    variants: {\n      size: {\n        xs: \"h-3 w-3\",\n        sm: \"h-4 w-4\",\n        default: \"h-5 w-5\",\n        lg: \"h-6 w-6\",\n        xl: \"h-8 w-8\",\n        \"2xl\": \"h-10 w-10\",\n      },\n      variant: {\n        default: \"text-current\",\n        primary: \"text-primary\",\n        secondary: \"text-secondary\",\n        muted: \"text-muted-foreground\",\n        success: \"text-success\",\n        warning: \"text-warning\",\n        error: \"text-error\",\n        info: \"text-info\",\n      },\n    },\n    defaultVariants: {\n      size: \"default\",\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface IconProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof iconVariants> {\n  icon: LucideIcon\n  spinning?: boolean\n}\n\nconst Icon = React.forwardRef<HTMLDivElement, IconProps>(\n  ({ className, size, variant, icon: IconComponent, spinning = false, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(iconVariants({ size, variant, className }), spinning && \"animate-spin\")}\n      {...props}\n    >\n      <IconComponent className=\"h-full w-full\" />\n    </div>\n  )\n)\nIcon.displayName = \"Icon\"\n\n// 预定义的图标组件\nexport const Icons = {\n  // 导航\n  home: Home,\n  search: Search,\n  user: User,\n  settings: Settings,\n  menu: Menu,\n  close: X,\n  chevronDown: ChevronDown,\n  chevronUp: ChevronUp,\n  chevronLeft: ChevronLeft,\n  chevronRight: ChevronRight,\n  arrowLeft: ArrowLeft,\n  arrowRight: ArrowRight,\n  arrowUpDown: ArrowUpDown,\n  \n  // 功能\n  download: Download,\n  upload: Upload,\n  share: Share,\n  heart: Heart,\n  star: Star,\n  bookmark: Bookmark,\n  eye: Eye,\n  eyeOff: EyeOff,\n  edit: Edit,\n  trash: Trash2,\n  copy: Copy,\n  check: Check,\n  plus: Plus,\n  minus: Minus,\n  \n  // 状态\n  checkCircle: CheckCircle,\n  alertCircle: AlertCircle,\n  info: Info,\n  warning: AlertTriangle,\n  clock: Clock,\n  calendar: Calendar,\n  mapPin: MapPin,\n  phone: Phone,\n  mail: Mail,\n  globe: Globe,\n  \n  // 媒体\n  play: Play,\n  pause: Pause,\n  volume: Volume2,\n  volumeOff: VolumeX,\n  image: Image,\n  video: Video,\n  music: Music,\n  file: File,\n  fileText: FileText,\n  \n  // 社交\n  message: MessageCircle,\n  send: Send,\n  thumbsUp: ThumbsUp,\n  thumbsDown: ThumbsDown,\n  users: Users,\n  userPlus: UserPlus,\n  \n  // 商业\n  cart: ShoppingCart,\n  creditCard: CreditCard,\n  dollar: DollarSign,\n  trendingUp: TrendingUp,\n  trendingDown: TrendingDown,\n  barChart: BarChart3,\n  pieChart: PieChart,\n  \n  // 系统\n  loading: Loader2,\n  refresh: RefreshCw,\n  power: Power,\n  wifi: Wifi,\n  wifiOff: WifiOff,\n  battery: Battery,\n  signal: Signal,\n  shield: Shield,\n  bell: Bell,\n  flag: Flag,\n  gift: Gift,\n  lock: Lock,\n  key: Key,\n  lightbulb: Lightbulb,\n  camera: Camera,\n  zap: Zap,\n  crown: Crown,\n  github: Github,\n  filter: Filter,\n\n  // 自定义图标\n  logo: ({ className, ...props }: React.SVGProps<SVGSVGElement>) => (\n    <svg\n      className={className}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      stroke=\"currentColor\"\n      strokeWidth=\"2\"\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      {...props}\n    >\n      <path d=\"M12 2L2 7l10 5 10-5-10-5z\" />\n      <path d=\"M2 17l10 5 10-5\" />\n      <path d=\"M2 12l10 5 10-5\" />\n    </svg>\n  ),\n  \n  // 应用相关图标\n  app: ({ className, ...props }: React.SVGProps<SVGSVGElement>) => (\n    <svg\n      className={className}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      stroke=\"currentColor\"\n      strokeWidth=\"2\"\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      {...props}\n    >\n      <rect x=\"3\" y=\"3\" width=\"7\" height=\"7\" />\n      <rect x=\"14\" y=\"3\" width=\"7\" height=\"7\" />\n      <rect x=\"14\" y=\"14\" width=\"7\" height=\"7\" />\n      <rect x=\"3\" y=\"14\" width=\"7\" height=\"7\" />\n    </svg>\n  ),\n  \n  // 邀请码图标\n  code: ({ className, ...props }: React.SVGProps<SVGSVGElement>) => (\n    <svg\n      className={className}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      stroke=\"currentColor\"\n      strokeWidth=\"2\"\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      {...props}\n    >\n      <polyline points=\"16,18 22,12 16,6\" />\n      <polyline points=\"8,6 2,12 8,18\" />\n    </svg>\n  ),\n  \n  // 活动图标\n  activity: ({ className, ...props }: React.SVGProps<SVGSVGElement>) => (\n    <svg\n      className={className}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      stroke=\"currentColor\"\n      strokeWidth=\"2\"\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      {...props}\n    >\n      <polyline points=\"22,12 18,12 15,21 9,3 6,12 2,12\" />\n    </svg>\n  ),\n}\n\nexport { Icon, iconVariants, type LucideIcon }\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;AAgGA,MAAM,eAAe,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACrB,2CACA;IACE,UAAU;QACR,MAAM;YACJ,IAAI;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,OAAO;QACT;QACA,SAAS;YACP,SAAS;YACT,SAAS;YACT,WAAW;YACX,OAAO;YACP,SAAS;YACT,SAAS;YACT,OAAO;YACP,MAAM;QACR;IACF;IACA,iBAAiB;QACf,MAAM;QACN,SAAS;IACX;AACF;AAUF,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC1B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,aAAa,EAAE,WAAW,KAAK,EAAE,GAAG,OAAO,EAAE,oBAC9E,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAAE;YAAM;YAAS;QAAU,IAAI,YAAY;QACrE,GAAG,KAAK;kBAET,cAAA,6LAAC;YAAc,WAAU;;;;;;;;;;;;AAI/B,KAAK,WAAW,GAAG;AAGZ,MAAM,QAAQ;IACnB,KAAK;IACL,MAAM,sMAAA,CAAA,OAAI;IACV,QAAQ,yMAAA,CAAA,SAAM;IACd,MAAM,qMAAA,CAAA,OAAI;IACV,UAAU,6MAAA,CAAA,WAAQ;IAClB,MAAM,qMAAA,CAAA,OAAI;IACV,OAAO,+LAAA,CAAA,IAAC;IACR,aAAa,uNAAA,CAAA,cAAW;IACxB,WAAW,mNAAA,CAAA,YAAS;IACpB,aAAa,uNAAA,CAAA,cAAW;IACxB,cAAc,yNAAA,CAAA,eAAY;IAC1B,WAAW,mNAAA,CAAA,YAAS;IACpB,YAAY,qNAAA,CAAA,aAAU;IACtB,aAAa,2NAAA,CAAA,cAAW;IAExB,KAAK;IACL,UAAU,6MAAA,CAAA,WAAQ;IAClB,QAAQ,yMAAA,CAAA,SAAM;IACd,OAAO,uMAAA,CAAA,QAAK;IACZ,OAAO,uMAAA,CAAA,QAAK;IACZ,MAAM,qMAAA,CAAA,OAAI;IACV,UAAU,6MAAA,CAAA,WAAQ;IAClB,KAAK,mMAAA,CAAA,MAAG;IACR,QAAQ,6MAAA,CAAA,SAAM;IACd,MAAM,8MAAA,CAAA,OAAI;IACV,OAAO,6MAAA,CAAA,SAAM;IACb,MAAM,qMAAA,CAAA,OAAI;IACV,OAAO,uMAAA,CAAA,QAAK;IACZ,MAAM,qMAAA,CAAA,OAAI;IACV,OAAO,uMAAA,CAAA,QAAK;IAEZ,KAAK;IACL,aAAa,8NAAA,CAAA,cAAW;IACxB,aAAa,uNAAA,CAAA,cAAW;IACxB,MAAM,qMAAA,CAAA,OAAI;IACV,SAAS,2NAAA,CAAA,gBAAa;IACtB,OAAO,uMAAA,CAAA,QAAK;IACZ,UAAU,6MAAA,CAAA,WAAQ;IAClB,QAAQ,6MAAA,CAAA,SAAM;IACd,OAAO,uMAAA,CAAA,QAAK;IACZ,MAAM,qMAAA,CAAA,OAAI;IACV,OAAO,uMAAA,CAAA,QAAK;IAEZ,KAAK;IACL,MAAM,qMAAA,CAAA,OAAI;IACV,OAAO,uMAAA,CAAA,QAAK;IACZ,QAAQ,+MAAA,CAAA,UAAO;IACf,WAAW,+MAAA,CAAA,UAAO;IAClB,OAAO,uMAAA,CAAA,QAAK;IACZ,OAAO,uMAAA,CAAA,QAAK;IACZ,OAAO,uMAAA,CAAA,QAAK;IACZ,MAAM,qMAAA,CAAA,OAAI;IACV,UAAU,iNAAA,CAAA,WAAQ;IAElB,KAAK;IACL,SAAS,2NAAA,CAAA,gBAAa;IACtB,MAAM,qMAAA,CAAA,OAAI;IACV,UAAU,iNAAA,CAAA,WAAQ;IAClB,YAAY,qNAAA,CAAA,aAAU;IACtB,OAAO,uMAAA,CAAA,QAAK;IACZ,UAAU,iNAAA,CAAA,WAAQ;IAElB,KAAK;IACL,MAAM,yNAAA,CAAA,eAAY;IAClB,YAAY,qNAAA,CAAA,aAAU;IACtB,QAAQ,qNAAA,CAAA,aAAU;IAClB,YAAY,qNAAA,CAAA,aAAU;IACtB,cAAc,yNAAA,CAAA,eAAY;IAC1B,UAAU,qNAAA,CAAA,YAAS;IACnB,UAAU,iNAAA,CAAA,WAAQ;IAElB,KAAK;IACL,SAAS,oNAAA,CAAA,UAAO;IAChB,SAAS,mNAAA,CAAA,YAAS;IAClB,OAAO,uMAAA,CAAA,QAAK;IACZ,MAAM,qMAAA,CAAA,OAAI;IACV,SAAS,+MAAA,CAAA,UAAO;IAChB,SAAS,2MAAA,CAAA,UAAO;IAChB,QAAQ,yMAAA,CAAA,SAAM;IACd,QAAQ,yMAAA,CAAA,SAAM;IACd,MAAM,qMAAA,CAAA,OAAI;IACV,MAAM,qMAAA,CAAA,OAAI;IACV,MAAM,qMAAA,CAAA,OAAI;IACV,MAAM,qMAAA,CAAA,OAAI;IACV,KAAK,mMAAA,CAAA,MAAG;IACR,WAAW,+MAAA,CAAA,YAAS;IACpB,QAAQ,yMAAA,CAAA,SAAM;IACd,KAAK,mMAAA,CAAA,MAAG;IACR,OAAO,uMAAA,CAAA,QAAK;IACZ,QAAQ,yMAAA,CAAA,SAAM;IACd,QAAQ,yMAAA,CAAA,SAAM;IAEd,QAAQ;IACR,MAAM,CAAC,EAAE,SAAS,EAAE,GAAG,OAAsC,iBAC3D,6LAAC;YACC,WAAW;YACX,SAAQ;YACR,MAAK;YACL,QAAO;YACP,aAAY;YACZ,eAAc;YACd,gBAAe;YACd,GAAG,KAAK;;8BAET,6LAAC;oBAAK,GAAE;;;;;;8BACR,6LAAC;oBAAK,GAAE;;;;;;8BACR,6LAAC;oBAAK,GAAE;;;;;;;;;;;;IAIZ,SAAS;IACT,KAAK,CAAC,EAAE,SAAS,EAAE,GAAG,OAAsC,iBAC1D,6LAAC;YACC,WAAW;YACX,SAAQ;YACR,MAAK;YACL,QAAO;YACP,aAAY;YACZ,eAAc;YACd,gBAAe;YACd,GAAG,KAAK;;8BAET,6LAAC;oBAAK,GAAE;oBAAI,GAAE;oBAAI,OAAM;oBAAI,QAAO;;;;;;8BACnC,6LAAC;oBAAK,GAAE;oBAAK,GAAE;oBAAI,OAAM;oBAAI,QAAO;;;;;;8BACpC,6LAAC;oBAAK,GAAE;oBAAK,GAAE;oBAAK,OAAM;oBAAI,QAAO;;;;;;8BACrC,6LAAC;oBAAK,GAAE;oBAAI,GAAE;oBAAK,OAAM;oBAAI,QAAO;;;;;;;;;;;;IAIxC,QAAQ;IACR,MAAM,CAAC,EAAE,SAAS,EAAE,GAAG,OAAsC,iBAC3D,6LAAC;YACC,WAAW;YACX,SAAQ;YACR,MAAK;YACL,QAAO;YACP,aAAY;YACZ,eAAc;YACd,gBAAe;YACd,GAAG,KAAK;;8BAET,6LAAC;oBAAS,QAAO;;;;;;8BACjB,6LAAC;oBAAS,QAAO;;;;;;;;;;;;IAIrB,OAAO;IACP,UAAU,CAAC,EAAE,SAAS,EAAE,GAAG,OAAsC,iBAC/D,6LAAC;YACC,WAAW;YACX,SAAQ;YACR,MAAK;YACL,QAAO;YACP,aAAY;YACZ,eAAc;YACd,gBAAe;YACd,GAAG,KAAK;sBAET,cAAA,6LAAC;gBAAS,QAAO;;;;;;;;;;;AAGvB", "debugId": null}}, {"offset": {"line": 1131, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/components/ui/avatar.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/utils\"\n\nconst avatarVariants = cva(\n  \"relative flex shrink-0 overflow-hidden rounded-full\",\n  {\n    variants: {\n      size: {\n        sm: \"h-8 w-8\",\n        default: \"h-10 w-10\",\n        lg: \"h-12 w-12\",\n        xl: \"h-16 w-16\",\n        \"2xl\": \"h-20 w-20\",\n      },\n    },\n    defaultVariants: {\n      size: \"default\",\n    },\n  }\n)\n\nexport interface AvatarProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof avatarVariants> {}\n\nconst Avatar = React.forwardRef<HTMLDivElement, AvatarProps>(\n  ({ className, size, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(avatarVariants({ size, className }))}\n      {...props}\n    />\n  )\n)\nAvatar.displayName = \"Avatar\"\n\nconst AvatarImage = React.forwardRef<\n  HTMLImageElement,\n  React.ImgHTMLAttributes<HTMLImageElement>\n>(({ className, ...props }, ref) => (\n  <img\n    ref={ref}\n    className={cn(\"aspect-square h-full w-full object-cover\", className)}\n    {...props}\n  />\n))\nAvatarImage.displayName = \"AvatarImage\"\n\nconst AvatarFallback = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted text-muted-foreground font-medium\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatarFallback.displayName = \"AvatarFallback\"\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,uDACA;IACE,UAAU;QACR,MAAM;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,OAAO;QACT;IACF;IACA,iBAAiB;QACf,MAAM;IACR;AACF;AAOF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE,oBAC9B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAM;QAAU;QAC9C,GAAG,KAAK;;;;;;;AAIf,OAAO,WAAW,GAAG;AAErB,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,4CAA4C;QACzD,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1211, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-error text-error-foreground hover:bg-error/80\",\n        outline: \"text-foreground\",\n        success:\n          \"border-transparent bg-success text-success-foreground hover:bg-success/80\",\n        warning:\n          \"border-transparent bg-warning text-warning-foreground hover:bg-warning/80\",\n        info:\n          \"border-transparent bg-info text-info-foreground hover:bg-info/80\",\n      },\n      size: {\n        default: \"px-2.5 py-0.5 text-xs\",\n        sm: \"px-2 py-0.5 text-xs\",\n        lg: \"px-3 py-1 text-sm\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, size, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant, size }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;YACT,SACE;YACF,SACE;YACF,MACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,OAAmB;IAC/D,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;YAAS;QAAK,IAAI;QAAa,GAAG,KAAK;;;;;;AAE9E;KAJS", "debugId": null}}, {"offset": {"line": 1269, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/components/layout/header.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport Link from \"next/link\"\nimport { usePathname } from \"next/navigation\"\nimport { motion, AnimatePresence } from \"framer-motion\"\nimport { Button } from \"@/components/ui/button\"\nimport { Container, Flex } from \"@/components/ui/layout\"\nimport { Icons } from \"@/components/ui/icon\"\nimport { Avatar, AvatarImage, AvatarFallback } from \"@/components/ui/avatar\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { cn } from \"@/utils\"\n\ninterface NavItem {\n  label: string\n  href: string\n  icon?: keyof typeof Icons\n  badge?: string\n}\n\nconst navItems: NavItem[] = [\n  {\n    label: \"首页\",\n    href: \"/\",\n    icon: \"home\",\n  },\n  {\n    label: \"应用推荐\",\n    href: \"/apps\",\n    icon: \"app\",\n  },\n  {\n    label: \"送码活动\",\n    href: \"/activities\",\n    icon: \"code\",\n    badge: \"热门\",\n  },\n  {\n    label: \"社区\",\n    href: \"/community\",\n    icon: \"users\",\n  },\n]\n\ninterface HeaderProps {\n  className?: string\n}\n\nconst Header: React.FC<HeaderProps> = ({ className }) => {\n  const pathname = usePathname()\n  const [mounted, setMounted] = React.useState(false)\n  const [isScrolled, setIsScrolled] = React.useState(false)\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = React.useState(false)\n  const [isUserMenuOpen, setIsUserMenuOpen] = React.useState(false)\n\n  // 模拟用户状态\n  const [user, setUser] = React.useState<{\n    name: string\n    email: string\n    avatar: string\n  } | null>(null)\n\n  React.useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  React.useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 10)\n    }\n\n    window.addEventListener(\"scroll\", handleScroll)\n    return () => window.removeEventListener(\"scroll\", handleScroll)\n  }, [])\n\n  React.useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      const target = event.target as Element\n      if (!target.closest('[data-user-menu]')) {\n        setIsUserMenuOpen(false)\n      }\n      if (!target.closest('[data-mobile-menu]')) {\n        setIsMobileMenuOpen(false)\n      }\n    }\n\n    document.addEventListener(\"click\", handleClickOutside)\n    return () => document.removeEventListener(\"click\", handleClickOutside)\n  }, [])\n\n  if (!mounted) {\n    return (\n      <header className={cn(\"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\", className)}>\n        <Container>\n          <div className=\"flex h-16 items-center justify-between\">\n            <div className=\"flex items-center gap-6\">\n              <div className=\"h-8 w-32 bg-muted animate-pulse rounded\" />\n              <div className=\"hidden md:flex items-center gap-6\">\n                {Array.from({ length: 4 }).map((_, i) => (\n                  <div key={i} className=\"h-4 w-16 bg-muted animate-pulse rounded\" />\n                ))}\n              </div>\n            </div>\n            <div className=\"flex items-center gap-4\">\n              <div className=\"h-8 w-20 bg-muted animate-pulse rounded\" />\n              <div className=\"h-8 w-8 bg-muted animate-pulse rounded-full\" />\n            </div>\n          </div>\n        </Container>\n      </header>\n    )\n  }\n\n  return (\n    <header className={cn(\n      \"sticky top-0 z-50 w-full border-b transition-all duration-200\",\n      isScrolled \n        ? \"bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 shadow-sm\" \n        : \"bg-background\",\n      className\n    )}>\n      <Container>\n        <div className=\"flex h-16 items-center justify-between\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center gap-2 hover:opacity-80 transition-opacity\">\n            <Icons.logo className=\"h-8 w-8 text-primary\" />\n            <span className=\"text-xl font-bold bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent\">\n              ICBar\n            </span>\n          </Link>\n\n          {/* 桌面导航 */}\n          <nav className=\"hidden md:flex items-center gap-6\">\n            {navItems.map((item) => {\n              const IconComponent = item.icon ? Icons[item.icon] : null\n              const isActive = pathname === item.href\n\n              return (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className={cn(\n                    \"flex items-center gap-2 text-sm font-medium transition-colors relative group\",\n                    isActive\n                      ? \"text-foreground\"\n                      : \"text-muted-foreground hover:text-foreground\"\n                  )}\n                >\n                  {IconComponent && <IconComponent className=\"w-4 h-4\" />}\n                  <span>{item.label}</span>\n                  {item.badge && (\n                    <Badge variant=\"warning\" size=\"sm\" className=\"ml-1\">\n                      {item.badge}\n                    </Badge>\n                  )}\n                  <div className={cn(\n                    \"absolute -bottom-1 left-0 h-0.5 bg-primary transition-all duration-200\",\n                    isActive ? \"w-full\" : \"w-0 group-hover:w-full\"\n                  )} />\n                </Link>\n              )\n            })}\n          </nav>\n\n          {/* 右侧操作区 */}\n          <div className=\"flex items-center gap-4\">\n            {/* 搜索按钮 */}\n            <Button variant=\"ghost\" size=\"icon\" className=\"hidden sm:flex\">\n              <Icons.search className=\"w-4 h-4\" />\n            </Button>\n\n            {/* 用户菜单或登录按钮 */}\n            {user ? (\n              <div className=\"relative\" data-user-menu>\n                <Button\n                  variant=\"ghost\"\n                  className=\"flex items-center gap-2 px-2\"\n                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}\n                >\n                  <Avatar size=\"sm\">\n                    <AvatarImage src={user.avatar} alt={user.name} />\n                    <AvatarFallback>\n                      {user.name.slice(0, 2).toUpperCase()}\n                    </AvatarFallback>\n                  </Avatar>\n                  <Icons.chevronDown className={cn(\n                    \"w-4 h-4 transition-transform duration-200\",\n                    isUserMenuOpen && \"rotate-180\"\n                  )} />\n                </Button>\n\n                <AnimatePresence>\n                  {isUserMenuOpen && (\n                    <motion.div\n                      initial={{ opacity: 0, y: 10, scale: 0.95 }}\n                      animate={{ opacity: 1, y: 0, scale: 1 }}\n                      exit={{ opacity: 0, y: 10, scale: 0.95 }}\n                      transition={{ duration: 0.2 }}\n                      className=\"absolute right-0 top-full mt-2 w-56 bg-background border rounded-lg shadow-lg py-2\"\n                    >\n                      <div className=\"px-4 py-2 border-b\">\n                        <p className=\"font-medium\">{user.name}</p>\n                        <p className=\"text-sm text-muted-foreground\">{user.email}</p>\n                      </div>\n                      <Link href=\"/profile\" className=\"flex items-center gap-2 px-4 py-2 text-sm hover:bg-muted\">\n                        <Icons.user className=\"w-4 h-4\" />\n                        个人中心\n                      </Link>\n                      <Link href=\"/settings\" className=\"flex items-center gap-2 px-4 py-2 text-sm hover:bg-muted\">\n                        <Icons.settings className=\"w-4 h-4\" />\n                        设置\n                      </Link>\n                      <div className=\"border-t my-2\" />\n                      <button className=\"flex items-center gap-2 px-4 py-2 text-sm hover:bg-muted w-full text-left\">\n                        <Icons.power className=\"w-4 h-4\" />\n                        退出登录\n                      </button>\n                    </motion.div>\n                  )}\n                </AnimatePresence>\n              </div>\n            ) : (\n              <Button size=\"sm\">\n                <Icons.user className=\"w-4 h-4 mr-2\" />\n                登录\n              </Button>\n            )}\n\n            {/* 移动端菜单按钮 */}\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              className=\"md:hidden\"\n              data-mobile-menu\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            >\n              <Icons.menu className=\"w-4 h-4\" />\n            </Button>\n          </div>\n        </div>\n\n        {/* 移动端导航菜单 */}\n        <AnimatePresence>\n          {isMobileMenuOpen && (\n            <motion.div\n              initial={{ opacity: 0, height: 0 }}\n              animate={{ opacity: 1, height: \"auto\" }}\n              exit={{ opacity: 0, height: 0 }}\n              transition={{ duration: 0.2 }}\n              className=\"md:hidden border-t bg-background\"\n              data-mobile-menu\n            >\n              <nav className=\"py-4 space-y-2\">\n                {navItems.map((item) => {\n                  const IconComponent = item.icon ? Icons[item.icon] : null\n                  const isActive = pathname === item.href\n\n                  return (\n                    <Link\n                      key={item.href}\n                      href={item.href}\n                      className={cn(\n                        \"flex items-center gap-3 px-4 py-2 text-sm font-medium rounded-md transition-colors\",\n                        isActive\n                          ? \"text-foreground bg-muted\"\n                          : \"text-muted-foreground hover:text-foreground hover:bg-muted\"\n                      )}\n                      onClick={() => setIsMobileMenuOpen(false)}\n                    >\n                      {IconComponent && <IconComponent className=\"w-4 h-4\" />}\n                      <span>{item.label}</span>\n                      {item.badge && (\n                        <Badge variant=\"warning\" size=\"sm\" className=\"ml-auto\">\n                          {item.badge}\n                        </Badge>\n                      )}\n                    </Link>\n                  )\n                })}\n              </nav>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </Container>\n    </header>\n  )\n}\n\nexport { Header }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAXA;;;;;;;;;;;AAoBA,MAAM,WAAsB;IAC1B;QACE,OAAO;QACP,MAAM;QACN,MAAM;IACR;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM;IACR;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM;IACR;CACD;AAMD,MAAM,SAAgC,CAAC,EAAE,SAAS,EAAE;;IAClD,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IAC/D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IAE3D,SAAS;IACT,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAI3B;IAEV,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;4BAAE;YACd,WAAW;QACb;2BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;4BAAE;YACd,MAAM;iDAAe;oBACnB,cAAc,OAAO,OAAO,GAAG;gBACjC;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;4BAAE;YACd,MAAM;uDAAqB,CAAC;oBAC1B,MAAM,SAAS,MAAM,MAAM;oBAC3B,IAAI,CAAC,OAAO,OAAO,CAAC,qBAAqB;wBACvC,kBAAkB;oBACpB;oBACA,IAAI,CAAC,OAAO,OAAO,CAAC,uBAAuB;wBACzC,oBAAoB;oBACtB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,SAAS;YACnC;oCAAO,IAAM,SAAS,mBAAmB,CAAC,SAAS;;QACrD;2BAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAO,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,gHAAgH;sBACpI,cAAA,6LAAC,qIAAA,CAAA,YAAS;0BACR,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;8CACZ,MAAM,IAAI,CAAC;wCAAE,QAAQ;oCAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,6LAAC;4CAAY,WAAU;2CAAb;;;;;;;;;;;;;;;;sCAIhB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAM3B;IAEA,qBACE,6LAAC;QAAO,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAClB,iEACA,aACI,yFACA,iBACJ;kBAEA,cAAA,6LAAC,qIAAA,CAAA,YAAS;;8BACR,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC,mIAAA,CAAA,QAAK,CAAC,IAAI;oCAAC,WAAU;;;;;;8CACtB,6LAAC;oCAAK,WAAU;8CAA8F;;;;;;;;;;;;sCAMhH,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC;gCACb,MAAM,gBAAgB,KAAK,IAAI,GAAG,mIAAA,CAAA,QAAK,CAAC,KAAK,IAAI,CAAC,GAAG;gCACrD,MAAM,WAAW,aAAa,KAAK,IAAI;gCAEvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EACV,gFACA,WACI,oBACA;;wCAGL,+BAAiB,6LAAC;4CAAc,WAAU;;;;;;sDAC3C,6LAAC;sDAAM,KAAK,KAAK;;;;;;wCAChB,KAAK,KAAK,kBACT,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,WAAU;sDAC1C,KAAK,KAAK;;;;;;sDAGf,6LAAC;4CAAI,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EACf,0EACA,WAAW,WAAW;;;;;;;mCAlBnB,KAAK,IAAI;;;;;4BAsBpB;;;;;;sCAIF,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAO,WAAU;8CAC5C,cAAA,6LAAC,mIAAA,CAAA,QAAK,CAAC,MAAM;wCAAC,WAAU;;;;;;;;;;;gCAIzB,qBACC,6LAAC;oCAAI,WAAU;oCAAW,gBAAc;;sDACtC,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;4CACV,SAAS,IAAM,kBAAkB,CAAC;;8DAElC,6LAAC,qIAAA,CAAA,SAAM;oDAAC,MAAK;;sEACX,6LAAC,qIAAA,CAAA,cAAW;4DAAC,KAAK,KAAK,MAAM;4DAAE,KAAK,KAAK,IAAI;;;;;;sEAC7C,6LAAC,qIAAA,CAAA,iBAAc;sEACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,WAAW;;;;;;;;;;;;8DAGtC,6LAAC,mIAAA,CAAA,QAAK,CAAC,WAAW;oDAAC,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAC7B,6CACA,kBAAkB;;;;;;;;;;;;sDAItB,6LAAC,4LAAA,CAAA,kBAAe;sDACb,gCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG;oDAAI,OAAO;gDAAK;gDAC1C,SAAS;oDAAE,SAAS;oDAAG,GAAG;oDAAG,OAAO;gDAAE;gDACtC,MAAM;oDAAE,SAAS;oDAAG,GAAG;oDAAI,OAAO;gDAAK;gDACvC,YAAY;oDAAE,UAAU;gDAAI;gDAC5B,WAAU;;kEAEV,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAAe,KAAK,IAAI;;;;;;0EACrC,6LAAC;gEAAE,WAAU;0EAAiC,KAAK,KAAK;;;;;;;;;;;;kEAE1D,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;;0EAC9B,6LAAC,mIAAA,CAAA,QAAK,CAAC,IAAI;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAGpC,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAY,WAAU;;0EAC/B,6LAAC,mIAAA,CAAA,QAAK,CAAC,QAAQ;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAGxC,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAO,WAAU;;0EAChB,6LAAC,mIAAA,CAAA,QAAK,CAAC,KAAK;gEAAC,WAAU;;;;;;4DAAY;;;;;;;;;;;;;;;;;;;;;;;yDAQ7C,6LAAC,qIAAA,CAAA,SAAM;oCAAC,MAAK;;sDACX,6LAAC,mIAAA,CAAA,QAAK,CAAC,IAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAM3C,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,kBAAgB;oCAChB,SAAS,IAAM,oBAAoB,CAAC;8CAEpC,cAAA,6LAAC,mIAAA,CAAA,QAAK,CAAC,IAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAM5B,6LAAC,4LAAA,CAAA,kBAAe;8BACb,kCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAE;wBACjC,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAO;wBACtC,MAAM;4BAAE,SAAS;4BAAG,QAAQ;wBAAE;wBAC9B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;wBACV,kBAAgB;kCAEhB,cAAA,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC;gCACb,MAAM,gBAAgB,KAAK,IAAI,GAAG,mIAAA,CAAA,QAAK,CAAC,KAAK,IAAI,CAAC,GAAG;gCACrD,MAAM,WAAW,aAAa,KAAK,IAAI;gCAEvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EACV,sFACA,WACI,6BACA;oCAEN,SAAS,IAAM,oBAAoB;;wCAElC,+BAAiB,6LAAC;4CAAc,WAAU;;;;;;sDAC3C,6LAAC;sDAAM,KAAK,KAAK;;;;;;wCAChB,KAAK,KAAK,kBACT,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,WAAU;sDAC1C,KAAK,KAAK;;;;;;;mCAdV,KAAK,IAAI;;;;;4BAmBpB;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB;GA9OM;;QACa,qIAAA,CAAA,cAAW;;;KADxB", "debugId": null}}, {"offset": {"line": 1869, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/components/layout/footer.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport Link from \"next/link\"\nimport { motion } from \"framer-motion\"\nimport { Button } from \"@/components/ui/button\"\nimport { Container, Grid, Flex, Stack, Separator } from \"@/components/ui/layout\"\nimport { Icons } from \"@/components/ui/icon\"\nimport { cn } from \"@/utils\"\n\ninterface FooterLink {\n  label: string\n  href: string\n}\n\ninterface FooterSection {\n  title: string\n  links: FooterLink[]\n}\n\nconst footerSections: FooterSection[] = [\n  {\n    title: \"产品\",\n    links: [\n      { label: \"应用推荐\", href: \"/apps\" },\n      { label: \"送码活动\", href: \"/activities\" },\n      { label: \"VIP会员\", href: \"/vip\" },\n      { label: \"API文档\", href: \"/docs\" },\n    ],\n  },\n  {\n    title: \"社区\",\n    links: [\n      { label: \"用户论坛\", href: \"/community\" },\n      { label: \"开发者\", href: \"/developers\" },\n      { label: \"合作伙伴\", href: \"/partners\" },\n      { label: \"媒体资源\", href: \"/media\" },\n    ],\n  },\n  {\n    title: \"支持\",\n    links: [\n      { label: \"帮助中心\", href: \"/help\" },\n      { label: \"联系我们\", href: \"/contact\" },\n      { label: \"意见反馈\", href: \"/feedback\" },\n      { label: \"状态页面\", href: \"/status\" },\n    ],\n  },\n  {\n    title: \"公司\",\n    links: [\n      { label: \"关于我们\", href: \"/about\" },\n      { label: \"招聘信息\", href: \"/careers\" },\n      { label: \"新闻动态\", href: \"/news\" },\n      { label: \"投资者\", href: \"/investors\" },\n    ],\n  },\n]\n\nconst socialLinks = [\n  { icon: Icons.message, href: \"#\", label: \"微信\" },\n  { icon: Icons.share, href: \"#\", label: \"微博\" },\n  { icon: Icons.github, href: \"#\", label: \"GitHub\" },\n  { icon: Icons.mail, href: \"#\", label: \"邮箱\" },\n]\n\ninterface FooterProps {\n  className?: string\n}\n\nconst Footer: React.FC<FooterProps> = ({ className }) => {\n  const currentYear = new Date().getFullYear()\n\n  return (\n    <footer className={cn(\"bg-muted/30 border-t\", className)}>\n      <Container>\n        <div className=\"py-12 lg:py-16\">\n          {/* 主要内容区域 */}\n          <Grid cols={5} gap=\"lg\" className=\"mb-12\">\n            {/* 品牌信息 */}\n            <div className=\"lg:col-span-1\">\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                viewport={{ once: true }}\n                transition={{ duration: 0.5 }}\n                className=\"space-y-4\"\n              >\n                <Link href=\"/\" className=\"flex items-center gap-2\">\n                  <Icons.logo className=\"h-8 w-8 text-primary\" />\n                  <span className=\"text-xl font-bold bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent\">\n                    ICBar\n                  </span>\n                </Link>\n                \n                <p className=\"text-sm text-muted-foreground leading-relaxed\">\n                  专业的应用推荐平台，汇聚优质应用推荐，提供专属邀请码服务。让你轻松发现好应用，享受专属优惠。\n                </p>\n                \n                {/* 社交媒体链接 */}\n                <div className=\"flex items-center gap-3\">\n                  {socialLinks.map((social, index) => {\n                    const IconComponent = social.icon\n                    return (\n                      <motion.div\n                        key={social.label}\n                        initial={{ opacity: 0, scale: 0.8 }}\n                        whileInView={{ opacity: 1, scale: 1 }}\n                        viewport={{ once: true }}\n                        transition={{ duration: 0.3, delay: index * 0.1 }}\n                      >\n                        <Link href={social.href} aria-label={social.label}>\n                          <Button\n                            variant=\"ghost\"\n                            size=\"icon\"\n                            className=\"h-8 w-8 hover:bg-primary/10 hover:text-primary\"\n                          >\n                            <IconComponent className=\"h-4 w-4\" />\n                          </Button>\n                        </Link>\n                      </motion.div>\n                    )\n                  })}\n                </div>\n              </motion.div>\n            </div>\n\n            {/* 链接区域 */}\n            <div className=\"lg:col-span-4\">\n              <Grid cols={4} gap=\"lg\">\n                {footerSections.map((section, sectionIndex) => (\n                  <motion.div\n                    key={section.title}\n                    initial={{ opacity: 0, y: 20 }}\n                    whileInView={{ opacity: 1, y: 0 }}\n                    viewport={{ once: true }}\n                    transition={{ duration: 0.5, delay: sectionIndex * 0.1 }}\n                  >\n                    <Stack spacing=\"md\">\n                      <h3 className=\"font-semibold text-foreground\">{section.title}</h3>\n                      <Stack spacing=\"sm\">\n                        {section.links.map((link, linkIndex) => (\n                          <motion.div\n                            key={link.href}\n                            initial={{ opacity: 0, x: -10 }}\n                            whileInView={{ opacity: 1, x: 0 }}\n                            viewport={{ once: true }}\n                            transition={{ \n                              duration: 0.3, \n                              delay: sectionIndex * 0.1 + linkIndex * 0.05 \n                            }}\n                          >\n                            <Link\n                              href={link.href}\n                              className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\"\n                            >\n                              {link.label}\n                            </Link>\n                          </motion.div>\n                        ))}\n                      </Stack>\n                    </Stack>\n                  </motion.div>\n                ))}\n              </Grid>\n            </div>\n          </Grid>\n\n          {/* 分隔线 */}\n          <Separator className=\"mb-8\" />\n\n          {/* 底部信息 */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            viewport={{ once: true }}\n            transition={{ duration: 0.5, delay: 0.4 }}\n          >\n            <Flex justify=\"between\" align=\"center\" className=\"flex-col md:flex-row gap-4\">\n              <div className=\"flex items-center gap-6 text-sm text-muted-foreground\">\n                <span>&copy; {currentYear} ICBar. 保留所有权利.</span>\n                <Link href=\"/privacy\" className=\"hover:text-foreground transition-colors\">\n                  隐私政策\n                </Link>\n                <Link href=\"/terms\" className=\"hover:text-foreground transition-colors\">\n                  服务条款\n                </Link>\n                <Link href=\"/cookies\" className=\"hover:text-foreground transition-colors\">\n                  Cookie政策\n                </Link>\n              </div>\n              \n              <div className=\"flex items-center gap-4\">\n                <span className=\"text-sm text-muted-foreground\">\n                  Made with ❤️ in China\n                </span>\n                <div className=\"flex items-center gap-2 text-xs text-muted-foreground\">\n                  <div className=\"w-2 h-2 bg-success rounded-full animate-pulse\" />\n                  <span>服务正常</span>\n                </div>\n              </div>\n            </Flex>\n          </motion.div>\n        </div>\n      </Container>\n    </footer>\n  )\n}\n\nexport { Footer }\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;AAoBA,MAAM,iBAAkC;IACtC;QACE,OAAO;QACP,OAAO;YACL;gBAAE,OAAO;gBAAQ,MAAM;YAAQ;YAC/B;gBAAE,OAAO;gBAAQ,MAAM;YAAc;YACrC;gBAAE,OAAO;gBAAS,MAAM;YAAO;YAC/B;gBAAE,OAAO;gBAAS,MAAM;YAAQ;SACjC;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,OAAO;gBAAQ,MAAM;YAAa;YACpC;gBAAE,OAAO;gBAAO,MAAM;YAAc;YACpC;gBAAE,OAAO;gBAAQ,MAAM;YAAY;YACnC;gBAAE,OAAO;gBAAQ,MAAM;YAAS;SACjC;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,OAAO;gBAAQ,MAAM;YAAQ;YAC/B;gBAAE,OAAO;gBAAQ,MAAM;YAAW;YAClC;gBAAE,OAAO;gBAAQ,MAAM;YAAY;YACnC;gBAAE,OAAO;gBAAQ,MAAM;YAAU;SAClC;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,OAAO;gBAAQ,MAAM;YAAS;YAChC;gBAAE,OAAO;gBAAQ,MAAM;YAAW;YAClC;gBAAE,OAAO;gBAAQ,MAAM;YAAQ;YAC/B;gBAAE,OAAO;gBAAO,MAAM;YAAa;SACpC;IACH;CACD;AAED,MAAM,cAAc;IAClB;QAAE,MAAM,mIAAA,CAAA,QAAK,CAAC,OAAO;QAAE,MAAM;QAAK,OAAO;IAAK;IAC9C;QAAE,MAAM,mIAAA,CAAA,QAAK,CAAC,KAAK;QAAE,MAAM;QAAK,OAAO;IAAK;IAC5C;QAAE,MAAM,mIAAA,CAAA,QAAK,CAAC,MAAM;QAAE,MAAM;QAAK,OAAO;IAAS;IACjD;QAAE,MAAM,mIAAA,CAAA,QAAK,CAAC,IAAI;QAAE,MAAM;QAAK,OAAO;IAAK;CAC5C;AAMD,MAAM,SAAgC,CAAC,EAAE,SAAS,EAAE;IAClD,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,qBACE,6LAAC;QAAO,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,wBAAwB;kBAC5C,cAAA,6LAAC,qIAAA,CAAA,YAAS;sBACR,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,qIAAA,CAAA,OAAI;wBAAC,MAAM;wBAAG,KAAI;wBAAK,WAAU;;0CAEhC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,WAAU;;sDAEV,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;8DACvB,6LAAC,mIAAA,CAAA,QAAK,CAAC,IAAI;oDAAC,WAAU;;;;;;8DACtB,6LAAC;oDAAK,WAAU;8DAA8F;;;;;;;;;;;;sDAKhH,6LAAC;4CAAE,WAAU;sDAAgD;;;;;;sDAK7D,6LAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC,QAAQ;gDACxB,MAAM,gBAAgB,OAAO,IAAI;gDACjC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDAET,SAAS;wDAAE,SAAS;wDAAG,OAAO;oDAAI;oDAClC,aAAa;wDAAE,SAAS;wDAAG,OAAO;oDAAE;oDACpC,UAAU;wDAAE,MAAM;oDAAK;oDACvB,YAAY;wDAAE,UAAU;wDAAK,OAAO,QAAQ;oDAAI;8DAEhD,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAM,OAAO,IAAI;wDAAE,cAAY,OAAO,KAAK;kEAC/C,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,WAAU;sEAEV,cAAA,6LAAC;gEAAc,WAAU;;;;;;;;;;;;;;;;mDAZxB,OAAO,KAAK;;;;;4CAiBvB;;;;;;;;;;;;;;;;;0CAMN,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qIAAA,CAAA,OAAI;oCAAC,MAAM;oCAAG,KAAI;8CAChB,eAAe,GAAG,CAAC,CAAC,SAAS,6BAC5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,UAAU;gDAAE,MAAM;4CAAK;4CACvB,YAAY;gDAAE,UAAU;gDAAK,OAAO,eAAe;4CAAI;sDAEvD,cAAA,6LAAC,qIAAA,CAAA,QAAK;gDAAC,SAAQ;;kEACb,6LAAC;wDAAG,WAAU;kEAAiC,QAAQ,KAAK;;;;;;kEAC5D,6LAAC,qIAAA,CAAA,QAAK;wDAAC,SAAQ;kEACZ,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,0BACxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gEAET,SAAS;oEAAE,SAAS;oEAAG,GAAG,CAAC;gEAAG;gEAC9B,aAAa;oEAAE,SAAS;oEAAG,GAAG;gEAAE;gEAChC,UAAU;oEAAE,MAAM;gEAAK;gEACvB,YAAY;oEACV,UAAU;oEACV,OAAO,eAAe,MAAM,YAAY;gEAC1C;0EAEA,cAAA,6LAAC,+JAAA,CAAA,UAAI;oEACH,MAAM,KAAK,IAAI;oEACf,WAAU;8EAET,KAAK,KAAK;;;;;;+DAbR,KAAK,IAAI;;;;;;;;;;;;;;;;2CAXjB,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;kCAqC5B,6LAAC,qIAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;kCAGrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;kCAExC,cAAA,6LAAC,qIAAA,CAAA,OAAI;4BAAC,SAAQ;4BAAU,OAAM;4BAAS,WAAU;;8CAC/C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;gDAAK;gDAAQ;gDAAY;;;;;;;sDAC1B,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;sDAA0C;;;;;;sDAG1E,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;sDAA0C;;;;;;sDAGxE,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;sDAA0C;;;;;;;;;;;;8CAK5E,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAgC;;;;;;sDAGhD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxB;KAzIM", "debugId": null}}, {"offset": {"line": 2378, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/components/sections/hero.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { motion } from \"framer-motion\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Container, Flex, Stack } from \"@/components/ui/layout\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Icons } from \"@/components/ui/icon\"\nimport { cn } from \"@/utils\"\n\ninterface HeroProps {\n  className?: string\n}\n\nconst Hero: React.FC<HeroProps> = ({ className }) => {\n  const [mounted, setMounted] = React.useState(false)\n\n  React.useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  if (!mounted) {\n    return (\n      <section className={cn(\"relative overflow-hidden bg-gradient-to-br from-primary/5 via-background to-secondary/5 py-20 lg:py-32\", className)}>\n        <Container>\n          <div className=\"text-center space-y-8\">\n            <div className=\"h-8 w-32 bg-muted animate-pulse rounded-full mx-auto\" />\n            <div className=\"space-y-4\">\n              <div className=\"h-12 w-3/4 bg-muted animate-pulse rounded mx-auto\" />\n              <div className=\"h-6 w-2/3 bg-muted animate-pulse rounded mx-auto\" />\n            </div>\n            <div className=\"flex justify-center gap-4\">\n              <div className=\"h-10 w-32 bg-muted animate-pulse rounded\" />\n              <div className=\"h-10 w-32 bg-muted animate-pulse rounded\" />\n            </div>\n          </div>\n        </Container>\n      </section>\n    )\n  }\n\n  return (\n    <section className={cn(\"relative overflow-hidden bg-gradient-to-br from-primary/5 via-background to-secondary/5 py-20 lg:py-32\", className)}>\n      {/* 背景装饰 */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute -top-40 -right-32 w-80 h-80 bg-primary/10 rounded-full blur-3xl\" />\n        <div className=\"absolute -bottom-40 -left-32 w-80 h-80 bg-secondary/10 rounded-full blur-3xl\" />\n        <div className=\"absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-accent/5 rounded-full blur-3xl\" />\n      </div>\n\n      <Container className=\"relative\">\n        <div className=\"text-center space-y-8\">\n          {/* 标签 */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5 }}\n          >\n            <Badge variant=\"outline\" className=\"px-4 py-2 text-sm font-medium\">\n              <Icons.activity className=\"w-4 h-4 mr-2\" />\n              发现优质应用，获取专属邀请码\n            </Badge>\n          </motion.div>\n\n          {/* 主标题 */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5, delay: 0.1 }}\n            className=\"space-y-4\"\n          >\n            <h1 className=\"text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight\">\n              <span className=\"bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent\">\n                ICBar\n              </span>\n              <br />\n              <span className=\"text-foreground\">\n                应用推荐平台\n              </span>\n            </h1>\n            <p className=\"text-lg md:text-xl text-muted-foreground max-w-2xl mx-auto leading-relaxed\">\n              汇聚优质应用推荐，提供专属邀请码服务。\n              <br />\n              让你轻松发现好应用，享受专属优惠。\n            </p>\n          </motion.div>\n\n          {/* CTA按钮 */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5, delay: 0.2 }}\n          >\n            <Flex justify=\"center\" gap=\"md\" className=\"flex-col sm:flex-row\">\n              <Button \n                size=\"lg\" \n                variant=\"gradient\"\n                className=\"group\"\n                leftIcon={<Icons.search className=\"w-5 h-5 group-hover:scale-110 transition-transform\" />}\n              >\n                探索应用\n              </Button>\n              <Button \n                size=\"lg\" \n                variant=\"outline\"\n                className=\"group\"\n                leftIcon={<Icons.code className=\"w-5 h-5 group-hover:scale-110 transition-transform\" />}\n              >\n                获取邀请码\n              </Button>\n            </Flex>\n          </motion.div>\n\n          {/* 统计数据 */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5, delay: 0.3 }}\n            className=\"pt-8\"\n          >\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-2xl mx-auto\">\n              <div className=\"text-center\">\n                <div className=\"text-2xl md:text-3xl font-bold text-primary\">1000+</div>\n                <div className=\"text-sm text-muted-foreground mt-1\">优质应用</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl md:text-3xl font-bold text-primary\">5000+</div>\n                <div className=\"text-sm text-muted-foreground mt-1\">活跃用户</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl md:text-3xl font-bold text-primary\">10000+</div>\n                <div className=\"text-sm text-muted-foreground mt-1\">邀请码发放</div>\n              </div>\n            </div>\n          </motion.div>\n\n          {/* 滚动提示 */}\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ duration: 0.5, delay: 0.4 }}\n            className=\"pt-8\"\n          >\n            <motion.div\n              animate={{ y: [0, 8, 0] }}\n              transition={{ duration: 2, repeat: Infinity, ease: \"easeInOut\" }}\n              className=\"inline-flex items-center justify-center w-8 h-8 rounded-full border border-muted-foreground/30 text-muted-foreground\"\n            >\n              <Icons.chevronDown className=\"w-4 h-4\" />\n            </motion.div>\n          </motion.div>\n        </div>\n      </Container>\n    </section>\n  )\n}\n\nexport { Hero }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAcA,MAAM,OAA4B,CAAC,EAAE,SAAS,EAAE;;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;0BAAE;YACd,WAAW;QACb;yBAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAQ,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,0GAA0G;sBAC/H,cAAA,6LAAC,qIAAA,CAAA,YAAS;0BACR,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;;;;;;;sCAEjB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAM3B;IAEA,qBACE,6LAAC;QAAQ,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,0GAA0G;;0BAE/H,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,6LAAC,qIAAA,CAAA,YAAS;gBAAC,WAAU;0BACnB,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;sCAE5B,cAAA,6LAAC,oIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAU,WAAU;;kDACjC,6LAAC,mIAAA,CAAA,QAAK,CAAC,QAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;sCAM/C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;4CAAK,WAAU;sDAA4E;;;;;;sDAG5F,6LAAC;;;;;sDACD,6LAAC;4CAAK,WAAU;sDAAkB;;;;;;;;;;;;8CAIpC,6LAAC;oCAAE,WAAU;;wCAA6E;sDAExF,6LAAC;;;;;wCAAK;;;;;;;;;;;;;sCAMV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;sCAExC,cAAA,6LAAC,qIAAA,CAAA,OAAI;gCAAC,SAAQ;gCAAS,KAAI;gCAAK,WAAU;;kDACxC,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,WAAU;wCACV,wBAAU,6LAAC,mIAAA,CAAA,QAAK,CAAC,MAAM;4CAAC,WAAU;;;;;;kDACnC;;;;;;kDAGD,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,WAAU;wCACV,wBAAU,6LAAC,mIAAA,CAAA,QAAK,CAAC,IAAI;4CAAC,WAAU;;;;;;kDACjC;;;;;;;;;;;;;;;;;sCAOL,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAA8C;;;;;;0DAC7D,6LAAC;gDAAI,WAAU;0DAAqC;;;;;;;;;;;;kDAEtD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAA8C;;;;;;0DAC7D,6LAAC;gDAAI,WAAU;0DAAqC;;;;;;;;;;;;kDAEtD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAA8C;;;;;;0DAC7D,6LAAC;gDAAI,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;sCAM1D,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;sCAEV,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,GAAG;wCAAC;wCAAG;wCAAG;qCAAE;gCAAC;gCACxB,YAAY;oCAAE,UAAU;oCAAG,QAAQ;oCAAU,MAAM;gCAAY;gCAC/D,WAAU;0CAEV,cAAA,6LAAC,mIAAA,CAAA,QAAK,CAAC,WAAW;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3C;GA7IM;KAAA", "debugId": null}}, {"offset": {"line": 2865, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/utils\"\n\nconst cardVariants = cva(\n  \"rounded-lg border bg-card text-card-foreground shadow-sm transition-all duration-200\",\n  {\n    variants: {\n      variant: {\n        default: \"border-border\",\n        elevated: \"shadow-md hover:shadow-lg\",\n        interactive: \"cursor-pointer hover:shadow-md hover:scale-[1.02] active:scale-[0.98]\",\n        outline: \"border-2 border-primary/20 hover:border-primary/40\",\n        ghost: \"border-transparent shadow-none hover:bg-muted/50\",\n      },\n      padding: {\n        none: \"p-0\",\n        sm: \"p-3\",\n        default: \"p-6\",\n        lg: \"p-8\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      padding: \"default\",\n    },\n  }\n)\n\nexport interface CardProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof cardVariants> {}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, variant, padding, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(cardVariants({ variant, padding, className }))}\n      {...props}\n    />\n  )\n)\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,eAAe,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACrB,wFACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,UAAU;YACV,aAAa;YACb,SAAS;YACT,OAAO;QACT;QACA,SAAS;YACP,MAAM;YACN,IAAI;YACJ,SAAS;YACT,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,SAAS;IACX;AACF;AAOF,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC1B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC1C,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAAE;YAAS;YAAS;QAAU;QACxD,GAAG,KAAK;;;;;;;AAIf,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2995, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/components/sections/features.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { motion } from \"framer-motion\"\nimport { Card, CardContent } from \"@/components/ui/card\"\nimport { Container, Grid } from \"@/components/ui/layout\"\nimport { Icons } from \"@/components/ui/icon\"\nimport { cn } from \"@/utils\"\n\ninterface Feature {\n  icon: keyof typeof Icons\n  title: string\n  description: string\n  color: string\n}\n\nconst features: Feature[] = [\n  {\n    icon: \"app\",\n    title: \"精选应用推荐\",\n    description: \"专业团队精心筛选，为你推荐最优质的应用，节省你的时间和精力。\",\n    color: \"text-blue-500\",\n  },\n  {\n    icon: \"code\",\n    title: \"专属邀请码\",\n    description: \"获取应用专属邀请码，享受独家优惠和特权，让你的体验更加超值。\",\n    color: \"text-green-500\",\n  },\n  {\n    icon: \"users\",\n    title: \"社区互动\",\n    description: \"与其他用户分享使用心得，参与讨论，发现更多有趣的应用和功能。\",\n    color: \"text-purple-500\",\n  },\n  {\n    icon: \"star\",\n    title: \"个性化推荐\",\n    description: \"基于你的使用习惯和偏好，智能推荐最适合你的应用和活动。\",\n    color: \"text-orange-500\",\n  },\n  {\n    icon: \"shield\",\n    title: \"安全可靠\",\n    description: \"所有推荐应用都经过安全检测，保障你的设备和数据安全。\",\n    color: \"text-red-500\",\n  },\n  {\n    icon: \"clock\",\n    title: \"实时更新\",\n    description: \"第一时间获取最新的应用信息和邀请码活动，不错过任何优惠机会。\",\n    color: \"text-cyan-500\",\n  },\n]\n\ninterface FeaturesProps {\n  className?: string\n}\n\nconst Features: React.FC<FeaturesProps> = ({ className }) => {\n  const [mounted, setMounted] = React.useState(false)\n\n  React.useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  if (!mounted) {\n    return (\n      <section className={cn(\"py-20 lg:py-32\", className)}>\n        <Container>\n          <div className=\"text-center space-y-4 mb-16\">\n            <div className=\"h-8 w-48 bg-muted animate-pulse rounded mx-auto\" />\n            <div className=\"h-6 w-96 bg-muted animate-pulse rounded mx-auto\" />\n          </div>\n          <Grid cols={3} gap=\"lg\">\n            {Array.from({ length: 6 }).map((_, i) => (\n              <Card key={i} className=\"p-6\">\n                <div className=\"space-y-4\">\n                  <div className=\"h-12 w-12 bg-muted animate-pulse rounded\" />\n                  <div className=\"h-6 w-32 bg-muted animate-pulse rounded\" />\n                  <div className=\"space-y-2\">\n                    <div className=\"h-4 w-full bg-muted animate-pulse rounded\" />\n                    <div className=\"h-4 w-3/4 bg-muted animate-pulse rounded\" />\n                  </div>\n                </div>\n              </Card>\n            ))}\n          </Grid>\n        </Container>\n      </section>\n    )\n  }\n\n  return (\n    <section className={cn(\"py-20 lg:py-32 bg-muted/30\", className)}>\n      <Container>\n        {/* 标题区域 */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.5 }}\n          className=\"text-center space-y-4 mb-16\"\n        >\n          <h2 className=\"text-3xl md:text-4xl font-bold tracking-tight\">\n            为什么选择 ICBar？\n          </h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n            我们致力于为用户提供最优质的应用推荐服务，让你轻松发现和使用优秀的应用\n          </p>\n        </motion.div>\n\n        {/* 特色功能网格 */}\n        <Grid cols={3} gap=\"lg\">\n          {features.map((feature, index) => {\n            const IconComponent = Icons[feature.icon]\n            \n            return (\n              <motion.div\n                key={feature.title}\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                viewport={{ once: true }}\n                transition={{ duration: 0.5, delay: index * 0.1 }}\n              >\n                <Card \n                  variant=\"interactive\" \n                  className=\"h-full group hover:shadow-lg transition-all duration-300\"\n                >\n                  <CardContent className=\"p-6\">\n                    <div className=\"space-y-4\">\n                      {/* 图标 */}\n                      <div className={cn(\n                        \"inline-flex items-center justify-center w-12 h-12 rounded-lg bg-background shadow-sm group-hover:scale-110 transition-transform duration-300\",\n                        feature.color\n                      )}>\n                        <IconComponent className=\"w-6 h-6\" />\n                      </div>\n\n                      {/* 标题 */}\n                      <h3 className=\"text-xl font-semibold group-hover:text-primary transition-colors\">\n                        {feature.title}\n                      </h3>\n\n                      {/* 描述 */}\n                      <p className=\"text-muted-foreground leading-relaxed\">\n                        {feature.description}\n                      </p>\n                    </div>\n                  </CardContent>\n                </Card>\n              </motion.div>\n            )\n          })}\n        </Grid>\n\n        {/* 底部CTA */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.5, delay: 0.6 }}\n          className=\"text-center mt-16\"\n        >\n          <div className=\"inline-flex items-center gap-2 px-6 py-3 bg-primary/10 rounded-full text-primary font-medium\">\n            <Icons.heart className=\"w-5 h-5\" />\n            <span>已有 5000+ 用户信赖我们的服务</span>\n          </div>\n        </motion.div>\n      </Container>\n    </section>\n  )\n}\n\nexport { Features }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AAgBA,MAAM,WAAsB;IAC1B;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;IACT;CACD;AAMD,MAAM,WAAoC,CAAC,EAAE,SAAS,EAAE;;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;8BAAE;YACd,WAAW;QACb;6BAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAQ,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;sBACvC,cAAA,6LAAC,qIAAA,CAAA,YAAS;;kCACR,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;kCAEjB,6LAAC,qIAAA,CAAA,OAAI;wBAAC,MAAM;wBAAG,KAAI;kCAChB,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,6LAAC,mIAAA,CAAA,OAAI;gCAAS,WAAU;0CACtB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;+BANV;;;;;;;;;;;;;;;;;;;;;IAevB;IAEA,qBACE,6LAAC;QAAQ,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;kBACnD,cAAA,6LAAC,qIAAA,CAAA,YAAS;;8BAER,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;sCAAgD;;;;;;sCAG9D,6LAAC;4BAAE,WAAU;sCAAkD;;;;;;;;;;;;8BAMjE,6LAAC,qIAAA,CAAA,OAAI;oBAAC,MAAM;oBAAG,KAAI;8BAChB,SAAS,GAAG,CAAC,CAAC,SAAS;wBACtB,MAAM,gBAAgB,mIAAA,CAAA,QAAK,CAAC,QAAQ,IAAI,CAAC;wBAEzC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;sCAEhD,cAAA,6LAAC,mIAAA,CAAA,OAAI;gCACH,SAAQ;gCACR,WAAU;0CAEV,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EACf,gJACA,QAAQ,KAAK;0DAEb,cAAA,6LAAC;oDAAc,WAAU;;;;;;;;;;;0DAI3B,6LAAC;gDAAG,WAAU;0DACX,QAAQ,KAAK;;;;;;0DAIhB,6LAAC;gDAAE,WAAU;0DACV,QAAQ,WAAW;;;;;;;;;;;;;;;;;;;;;;2BA3BvB,QAAQ,KAAK;;;;;oBAkCxB;;;;;;8BAIF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mIAAA,CAAA,QAAK,CAAC,KAAK;gCAAC,WAAU;;;;;;0CACvB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlB;GAjHM;KAAA", "debugId": null}}, {"offset": {"line": 3363, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/components/sections/app-showcase.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { motion } from \"framer-motion\"\nimport { <PERSON>, Card<PERSON>ontent, CardFooter } from \"@/components/ui/card\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Avatar, AvatarImage, AvatarFallback } from \"@/components/ui/avatar\"\nimport { Container, Grid, Flex } from \"@/components/ui/layout\"\nimport { Icons } from \"@/components/ui/icon\"\nimport { cn, formatNumber, generateAppIcon } from \"@/utils\"\n\ninterface App {\n  id: string\n  name: string\n  description: string\n  icon: string\n  category: string\n  rating: number\n  downloads: number\n  activePromotions: number\n  featured: boolean\n  trending: boolean\n}\n\n\n\nconst mockApps: App[] = [\n  {\n    id: \"1\",\n    name: \"Notion\",\n    description: \"强大的笔记和协作工具，让团队工作更高效\",\n    icon: generateAppIcon(\"Notion\", \"#000000\"),\n    category: \"效率工具\",\n    rating: 4.8,\n    downloads: 1200000,\n    activePromotions: 3,\n    featured: true,\n    trending: true,\n  },\n  {\n    id: \"2\",\n    name: \"Figma\",\n    description: \"专业的UI/UX设计工具，支持实时协作\",\n    icon: generateAppIcon(\"Figma\", \"#F24E1E\"),\n    category: \"设计工具\",\n    rating: 4.9,\n    downloads: 800000,\n    activePromotions: 2,\n    featured: true,\n    trending: false,\n  },\n  {\n    id: \"3\",\n    name: \"Spotify\",\n    description: \"全球领先的音乐流媒体平台\",\n    icon: generateAppIcon(\"Spotify\", \"#1DB954\"),\n    category: \"娱乐\",\n    rating: 4.7,\n    downloads: 5000000,\n    activePromotions: 1,\n    featured: false,\n    trending: true,\n  },\n  {\n    id: \"4\",\n    name: \"Slack\",\n    description: \"企业级即时通讯和协作平台\",\n    icon: generateAppIcon(\"Slack\", \"#4A154B\"),\n    category: \"商务\",\n    rating: 4.6,\n    downloads: 2000000,\n    activePromotions: 4,\n    featured: true,\n    trending: false,\n  },\n  {\n    id: \"5\",\n    name: \"Adobe Photoshop\",\n    description: \"专业图像编辑和设计软件\",\n    icon: generateAppIcon(\"Adobe Photoshop\", \"#31A8FF\"),\n    category: \"设计工具\",\n    rating: 4.5,\n    downloads: 3000000,\n    activePromotions: 2,\n    featured: false,\n    trending: true,\n  },\n  {\n    id: \"6\",\n    name: \"Zoom\",\n    description: \"高质量视频会议和远程协作工具\",\n    icon: generateAppIcon(\"Zoom\", \"#2D8CFF\"),\n    category: \"商务\",\n    rating: 4.4,\n    downloads: 4000000,\n    activePromotions: 1,\n    featured: true,\n    trending: false,\n  },\n]\n\ninterface AppShowcaseProps {\n  className?: string\n}\n\nconst AppShowcase: React.FC<AppShowcaseProps> = ({ className }) => {\n  const [mounted, setMounted] = React.useState(false)\n\n  React.useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  if (!mounted) {\n    return (\n      <section className={cn(\"py-20 lg:py-32\", className)}>\n        <Container>\n          <div className=\"text-center space-y-4 mb-16\">\n            <div className=\"h-8 w-48 bg-muted animate-pulse rounded mx-auto\" />\n            <div className=\"h-6 w-96 bg-muted animate-pulse rounded mx-auto\" />\n          </div>\n          <Grid cols={3} gap=\"lg\">\n            {Array.from({ length: 6 }).map((_, i) => (\n              <Card key={i} className=\"p-6\">\n                <div className=\"space-y-4\">\n                  <div className=\"flex items-center gap-3\">\n                    <div className=\"h-12 w-12 bg-muted animate-pulse rounded-lg\" />\n                    <div className=\"space-y-2 flex-1\">\n                      <div className=\"h-5 w-24 bg-muted animate-pulse rounded\" />\n                      <div className=\"h-4 w-16 bg-muted animate-pulse rounded\" />\n                    </div>\n                  </div>\n                  <div className=\"space-y-2\">\n                    <div className=\"h-4 w-full bg-muted animate-pulse rounded\" />\n                    <div className=\"h-4 w-3/4 bg-muted animate-pulse rounded\" />\n                  </div>\n                  <div className=\"h-10 w-full bg-muted animate-pulse rounded\" />\n                </div>\n              </Card>\n            ))}\n          </Grid>\n        </Container>\n      </section>\n    )\n  }\n\n  return (\n    <section className={cn(\"py-20 lg:py-32\", className)}>\n      <Container>\n        {/* 标题区域 */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.5 }}\n          className=\"text-center space-y-4 mb-16\"\n        >\n          <h2 className=\"text-3xl md:text-4xl font-bold tracking-tight\">\n            热门应用推荐\n          </h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n            精选优质应用，每一个都经过我们的严格筛选，为你带来最佳的使用体验\n          </p>\n        </motion.div>\n\n        {/* 应用网格 */}\n        <Grid cols={3} gap=\"lg\">\n          {mockApps.map((app, index) => (\n            <motion.div\n              key={app.id}\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              viewport={{ once: true }}\n              transition={{ duration: 0.5, delay: index * 0.1 }}\n            >\n              <Card variant=\"interactive\" className=\"h-full group\">\n                <CardContent className=\"p-6\">\n                  {/* 应用头部 */}\n                  <div className=\"flex items-start gap-4 mb-4\">\n                    <Avatar size=\"lg\">\n                      <AvatarImage src={app.icon} alt={app.name} />\n                      <AvatarFallback>\n                        {app.name.slice(0, 2).toUpperCase()}\n                      </AvatarFallback>\n                    </Avatar>\n                    \n                    <div className=\"flex-1 min-w-0\">\n                      <div className=\"flex items-center gap-2 mb-1\">\n                        <h3 className=\"font-semibold text-lg truncate group-hover:text-primary transition-colors\">\n                          {app.name}\n                        </h3>\n                        {app.featured && (\n                          <Badge variant=\"success\" size=\"sm\">\n                            <Icons.star className=\"w-3 h-3 mr-1\" />\n                            精选\n                          </Badge>\n                        )}\n                        {app.trending && (\n                          <Badge variant=\"warning\" size=\"sm\">\n                            <Icons.trendingUp className=\"w-3 h-3 mr-1\" />\n                            热门\n                          </Badge>\n                        )}\n                      </div>\n                      \n                      <Badge variant=\"outline\" size=\"sm\">\n                        {app.category}\n                      </Badge>\n                    </div>\n                  </div>\n\n                  {/* 应用描述 */}\n                  <p className=\"text-muted-foreground text-sm mb-4 line-clamp-2\">\n                    {app.description}\n                  </p>\n\n                  {/* 统计信息 */}\n                  <div className=\"flex items-center justify-between text-sm text-muted-foreground mb-4\">\n                    <div className=\"flex items-center gap-1\">\n                      <Icons.star className=\"w-4 h-4 text-yellow-500\" />\n                      <span>{app.rating}</span>\n                    </div>\n                    <div className=\"flex items-center gap-1\">\n                      <Icons.download className=\"w-4 h-4\" />\n                      <span>{formatNumber(app.downloads)}</span>\n                    </div>\n                    <div className=\"flex items-center gap-1\">\n                      <Icons.code className=\"w-4 h-4 text-primary\" />\n                      <span>{app.activePromotions} 个活动</span>\n                    </div>\n                  </div>\n                </CardContent>\n\n                <CardFooter className=\"p-6 pt-0\">\n                  <Flex gap=\"sm\" className=\"w-full\">\n                    <Button variant=\"outline\" size=\"sm\" className=\"flex-1\">\n                      <Icons.eye className=\"w-4 h-4 mr-2\" />\n                      查看详情\n                    </Button>\n                    <Button size=\"sm\" className=\"flex-1\">\n                      <Icons.code className=\"w-4 h-4 mr-2\" />\n                      获取邀请码\n                    </Button>\n                  </Flex>\n                </CardFooter>\n              </Card>\n            </motion.div>\n          ))}\n        </Grid>\n\n        {/* 查看更多 */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.5, delay: 0.6 }}\n          className=\"text-center mt-12\"\n        >\n          <Button variant=\"outline\" size=\"lg\">\n            查看更多应用\n            <Icons.arrowRight className=\"w-4 h-4 ml-2\" />\n          </Button>\n        </motion.div>\n      </Container>\n    </section>\n  )\n}\n\nexport { AppShowcase }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;AA2BA,MAAM,WAAkB;IACtB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,CAAA,GAAA,wHAAA,CAAA,kBAAe,AAAD,EAAE,UAAU;QAChC,UAAU;QACV,QAAQ;QACR,WAAW;QACX,kBAAkB;QAClB,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,CAAA,GAAA,wHAAA,CAAA,kBAAe,AAAD,EAAE,SAAS;QAC/B,UAAU;QACV,QAAQ;QACR,WAAW;QACX,kBAAkB;QAClB,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,CAAA,GAAA,wHAAA,CAAA,kBAAe,AAAD,EAAE,WAAW;QACjC,UAAU;QACV,QAAQ;QACR,WAAW;QACX,kBAAkB;QAClB,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,CAAA,GAAA,wHAAA,CAAA,kBAAe,AAAD,EAAE,SAAS;QAC/B,UAAU;QACV,QAAQ;QACR,WAAW;QACX,kBAAkB;QAClB,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,CAAA,GAAA,wHAAA,CAAA,kBAAe,AAAD,EAAE,mBAAmB;QACzC,UAAU;QACV,QAAQ;QACR,WAAW;QACX,kBAAkB;QAClB,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,CAAA,GAAA,wHAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ;QAC9B,UAAU;QACV,QAAQ;QACR,WAAW;QACX,kBAAkB;QAClB,UAAU;QACV,UAAU;IACZ;CACD;AAMD,MAAM,cAA0C,CAAC,EAAE,SAAS,EAAE;;IAC5D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;iCAAE;YACd,WAAW;QACb;gCAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAQ,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;sBACvC,cAAA,6LAAC,qIAAA,CAAA,YAAS;;kCACR,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;kCAEjB,6LAAC,qIAAA,CAAA,OAAI;wBAAC,MAAM;wBAAG,KAAI;kCAChB,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,6LAAC,mIAAA,CAAA,OAAI;gCAAS,WAAU;0CACtB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;;;;;;;;;;;;;sDAGnB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;;;;;;;;;;;sDAEjB,6LAAC;4CAAI,WAAU;;;;;;;;;;;;+BAbR;;;;;;;;;;;;;;;;;;;;;IAqBvB;IAEA,qBACE,6LAAC;QAAQ,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;kBACvC,cAAA,6LAAC,qIAAA,CAAA,YAAS;;8BAER,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;sCAAgD;;;;;;sCAG9D,6LAAC;4BAAE,WAAU;sCAAkD;;;;;;;;;;;;8BAMjE,6LAAC,qIAAA,CAAA,OAAI;oBAAC,MAAM;oBAAG,KAAI;8BAChB,SAAS,GAAG,CAAC,CAAC,KAAK,sBAClB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;sCAEhD,cAAA,6LAAC,mIAAA,CAAA,OAAI;gCAAC,SAAQ;gCAAc,WAAU;;kDACpC,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DAErB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qIAAA,CAAA,SAAM;wDAAC,MAAK;;0EACX,6LAAC,qIAAA,CAAA,cAAW;gEAAC,KAAK,IAAI,IAAI;gEAAE,KAAK,IAAI,IAAI;;;;;;0EACzC,6LAAC,qIAAA,CAAA,iBAAc;0EACZ,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,WAAW;;;;;;;;;;;;kEAIrC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;kFACX,IAAI,IAAI;;;;;;oEAEV,IAAI,QAAQ,kBACX,6LAAC,oIAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAU,MAAK;;0FAC5B,6LAAC,mIAAA,CAAA,QAAK,CAAC,IAAI;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;oEAI1C,IAAI,QAAQ,kBACX,6LAAC,oIAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAU,MAAK;;0FAC5B,6LAAC,mIAAA,CAAA,QAAK,CAAC,UAAU;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;;;;;;;0EAMnD,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAU,MAAK;0EAC3B,IAAI,QAAQ;;;;;;;;;;;;;;;;;;0DAMnB,6LAAC;gDAAE,WAAU;0DACV,IAAI,WAAW;;;;;;0DAIlB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,mIAAA,CAAA,QAAK,CAAC,IAAI;gEAAC,WAAU;;;;;;0EACtB,6LAAC;0EAAM,IAAI,MAAM;;;;;;;;;;;;kEAEnB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,mIAAA,CAAA,QAAK,CAAC,QAAQ;gEAAC,WAAU;;;;;;0EAC1B,6LAAC;0EAAM,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,IAAI,SAAS;;;;;;;;;;;;kEAEnC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,mIAAA,CAAA,QAAK,CAAC,IAAI;gEAAC,WAAU;;;;;;0EACtB,6LAAC;;oEAAM,IAAI,gBAAgB;oEAAC;;;;;;;;;;;;;;;;;;;;;;;;;kDAKlC,6LAAC,mIAAA,CAAA,aAAU;wCAAC,WAAU;kDACpB,cAAA,6LAAC,qIAAA,CAAA,OAAI;4CAAC,KAAI;4CAAK,WAAU;;8DACvB,6LAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,MAAK;oDAAK,WAAU;;sEAC5C,6LAAC,mIAAA,CAAA,QAAK,CAAC,GAAG;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGxC,6LAAC,qIAAA,CAAA,SAAM;oDAAC,MAAK;oDAAK,WAAU;;sEAC1B,6LAAC,mIAAA,CAAA,QAAK,CAAC,IAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;2BAvE1C,IAAI,EAAE;;;;;;;;;;8BAkFjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAEV,cAAA,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,MAAK;;4BAAK;0CAElC,6LAAC,mIAAA,CAAA,QAAK,CAAC,UAAU;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMxC;GAhKM;KAAA", "debugId": null}}, {"offset": {"line": 4009, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/components/sections/stats.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { motion } from \"framer-motion\"\nimport { Card, CardContent } from \"@/components/ui/card\"\nimport { Container, Grid } from \"@/components/ui/layout\"\nimport { Icons } from \"@/components/ui/icon\"\nimport { cn, formatNumber } from \"@/utils\"\n\ninterface Stat {\n  icon: keyof typeof Icons\n  value: number\n  label: string\n  description: string\n  color: string\n  prefix?: string\n  suffix?: string\n}\n\nconst stats: Stat[] = [\n  {\n    icon: \"app\",\n    value: 1200,\n    label: \"优质应用\",\n    description: \"经过严格筛选的高质量应用\",\n    color: \"text-blue-500\",\n    suffix: \"+\",\n  },\n  {\n    icon: \"users\",\n    value: 15000,\n    label: \"活跃用户\",\n    description: \"每月活跃使用平台的用户数量\",\n    color: \"text-green-500\",\n    suffix: \"+\",\n  },\n  {\n    icon: \"code\",\n    value: 25000,\n    label: \"邀请码发放\",\n    description: \"累计为用户提供的邀请码数量\",\n    color: \"text-purple-500\",\n    suffix: \"+\",\n  },\n  {\n    icon: \"activity\",\n    value: 500,\n    label: \"活跃活动\",\n    description: \"正在进行中的送码活动\",\n    color: \"text-orange-500\",\n    suffix: \"+\",\n  },\n  {\n    icon: \"star\",\n    value: 98,\n    label: \"用户满意度\",\n    description: \"用户对平台服务的满意度评分\",\n    color: \"text-yellow-500\",\n    suffix: \"%\",\n  },\n  {\n    icon: \"trendingUp\",\n    value: 150,\n    label: \"月增长率\",\n    description: \"平台用户数量的月度增长率\",\n    color: \"text-red-500\",\n    suffix: \"%\",\n  },\n]\n\ninterface StatsProps {\n  className?: string\n}\n\nconst Stats: React.FC<StatsProps> = ({ className }) => {\n  const [mounted, setMounted] = React.useState(false)\n  const [counters, setCounters] = React.useState<number[]>(stats.map(() => 0))\n\n  React.useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  // 数字动画效果\n  React.useEffect(() => {\n    if (!mounted) return\n\n    const timers = stats.map((stat, index) => {\n      const duration = 2000 // 2秒动画\n      const steps = 60 // 60帧\n      const increment = stat.value / steps\n      let current = 0\n      let step = 0\n\n      return setInterval(() => {\n        step++\n        current = Math.min(current + increment, stat.value)\n        \n        setCounters(prev => {\n          const newCounters = [...prev]\n          newCounters[index] = Math.floor(current)\n          return newCounters\n        })\n\n        if (step >= steps) {\n          clearInterval(timers[index])\n        }\n      }, duration / steps)\n    })\n\n    return () => {\n      timers.forEach(timer => clearInterval(timer))\n    }\n  }, [mounted])\n\n  if (!mounted) {\n    return (\n      <section className={cn(\"py-20 lg:py-32 bg-muted/30\", className)}>\n        <Container>\n          <div className=\"text-center space-y-4 mb-16\">\n            <div className=\"h-8 w-48 bg-muted animate-pulse rounded mx-auto\" />\n            <div className=\"h-6 w-96 bg-muted animate-pulse rounded mx-auto\" />\n          </div>\n          <Grid cols={3} gap=\"lg\">\n            {Array.from({ length: 6 }).map((_, i) => (\n              <Card key={i} className=\"p-6 text-center\">\n                <div className=\"space-y-4\">\n                  <div className=\"h-12 w-12 bg-muted animate-pulse rounded-full mx-auto\" />\n                  <div className=\"h-8 w-16 bg-muted animate-pulse rounded mx-auto\" />\n                  <div className=\"h-5 w-24 bg-muted animate-pulse rounded mx-auto\" />\n                  <div className=\"h-4 w-32 bg-muted animate-pulse rounded mx-auto\" />\n                </div>\n              </Card>\n            ))}\n          </Grid>\n        </Container>\n      </section>\n    )\n  }\n\n  return (\n    <section className={cn(\"py-20 lg:py-32 bg-muted/30\", className)}>\n      <Container>\n        {/* 标题区域 */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.5 }}\n          className=\"text-center space-y-4 mb-16\"\n        >\n          <h2 className=\"text-3xl md:text-4xl font-bold tracking-tight\">\n            平台数据一览\n          </h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n            用数据说话，见证我们的成长和用户的信赖\n          </p>\n        </motion.div>\n\n        {/* 统计数据网格 */}\n        <Grid cols={3} gap=\"lg\">\n          {stats.map((stat, index) => {\n            const IconComponent = Icons[stat.icon]\n            \n            return (\n              <motion.div\n                key={stat.label}\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                viewport={{ once: true }}\n                transition={{ duration: 0.5, delay: index * 0.1 }}\n              >\n                <Card className=\"text-center group hover:shadow-lg transition-all duration-300\">\n                  <CardContent className=\"p-8\">\n                    {/* 图标 */}\n                    <div className={cn(\n                      \"inline-flex items-center justify-center w-16 h-16 rounded-full bg-background shadow-sm mb-6 group-hover:scale-110 transition-transform duration-300\",\n                      stat.color\n                    )}>\n                      <IconComponent className=\"w-8 h-8\" />\n                    </div>\n\n                    {/* 数值 */}\n                    <div className=\"space-y-2 mb-4\">\n                      <div className=\"text-4xl md:text-5xl font-bold text-foreground\">\n                        {stat.prefix}\n                        <motion.span\n                          key={counters[index]}\n                          initial={{ opacity: 0.5 }}\n                          animate={{ opacity: 1 }}\n                          transition={{ duration: 0.1 }}\n                        >\n                          {formatNumber(counters[index])}\n                        </motion.span>\n                        {stat.suffix}\n                      </div>\n                      <h3 className=\"text-xl font-semibold text-foreground\">\n                        {stat.label}\n                      </h3>\n                    </div>\n\n                    {/* 描述 */}\n                    <p className=\"text-muted-foreground text-sm leading-relaxed\">\n                      {stat.description}\n                    </p>\n                  </CardContent>\n                </Card>\n              </motion.div>\n            )\n          })}\n        </Grid>\n\n        {/* 底部说明 */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.5, delay: 0.6 }}\n          className=\"text-center mt-16\"\n        >\n          <div className=\"inline-flex items-center gap-2 px-6 py-3 bg-primary/10 rounded-full text-primary font-medium\">\n            <Icons.clock className=\"w-5 h-5\" />\n            <span>数据实时更新，每小时同步一次</span>\n          </div>\n        </motion.div>\n      </Container>\n    </section>\n  )\n}\n\nexport { Stats }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AAmBA,MAAM,QAAgB;IACpB;QACE,MAAM;QACN,OAAO;QACP,OAAO;QACP,aAAa;QACb,OAAO;QACP,QAAQ;IACV;IACA;QACE,MAAM;QACN,OAAO;QACP,OAAO;QACP,aAAa;QACb,OAAO;QACP,QAAQ;IACV;IACA;QACE,MAAM;QACN,OAAO;QACP,OAAO;QACP,aAAa;QACb,OAAO;QACP,QAAQ;IACV;IACA;QACE,MAAM;QACN,OAAO;QACP,OAAO;QACP,aAAa;QACb,OAAO;QACP,QAAQ;IACV;IACA;QACE,MAAM;QACN,OAAO;QACP,OAAO;QACP,aAAa;QACb,OAAO;QACP,QAAQ;IACV;IACA;QACE,MAAM;QACN,OAAO;QACP,OAAO;QACP,aAAa;QACb,OAAO;QACP,QAAQ;IACV;CACD;AAMD,MAAM,QAA8B,CAAC,EAAE,SAAS,EAAE;;IAChD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAY,MAAM,GAAG;0BAAC,IAAM;;IAEzE,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;2BAAE;YACd,WAAW;QACb;0BAAG,EAAE;IAEL,SAAS;IACT,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;2BAAE;YACd,IAAI,CAAC,SAAS;YAEd,MAAM,SAAS,MAAM,GAAG;0CAAC,CAAC,MAAM;oBAC9B,MAAM,WAAW,KAAK,OAAO;;oBAC7B,MAAM,QAAQ,GAAG,MAAM;;oBACvB,MAAM,YAAY,KAAK,KAAK,GAAG;oBAC/B,IAAI,UAAU;oBACd,IAAI,OAAO;oBAEX,OAAO;kDAAY;4BACjB;4BACA,UAAU,KAAK,GAAG,CAAC,UAAU,WAAW,KAAK,KAAK;4BAElD;0DAAY,CAAA;oCACV,MAAM,cAAc;2CAAI;qCAAK;oCAC7B,WAAW,CAAC,MAAM,GAAG,KAAK,KAAK,CAAC;oCAChC,OAAO;gCACT;;4BAEA,IAAI,QAAQ,OAAO;gCACjB,cAAc,MAAM,CAAC,MAAM;4BAC7B;wBACF;iDAAG,WAAW;gBAChB;;YAEA;mCAAO;oBACL,OAAO,OAAO;2CAAC,CAAA,QAAS,cAAc;;gBACxC;;QACF;0BAAG;QAAC;KAAQ;IAEZ,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAQ,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;sBACnD,cAAA,6LAAC,qIAAA,CAAA,YAAS;;kCACR,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;kCAEjB,6LAAC,qIAAA,CAAA,OAAI;wBAAC,MAAM;wBAAG,KAAI;kCAChB,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,6LAAC,mIAAA,CAAA,OAAI;gCAAS,WAAU;0CACtB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;+BALR;;;;;;;;;;;;;;;;;;;;;IAavB;IAEA,qBACE,6LAAC;QAAQ,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;kBACnD,cAAA,6LAAC,qIAAA,CAAA,YAAS;;8BAER,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;sCAAgD;;;;;;sCAG9D,6LAAC;4BAAE,WAAU;sCAAkD;;;;;;;;;;;;8BAMjE,6LAAC,qIAAA,CAAA,OAAI;oBAAC,MAAM;oBAAG,KAAI;8BAChB,MAAM,GAAG,CAAC,CAAC,MAAM;wBAChB,MAAM,gBAAgB,mIAAA,CAAA,QAAK,CAAC,KAAK,IAAI,CAAC;wBAEtC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;sCAEhD,cAAA,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDAErB,6LAAC;4CAAI,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EACf,uJACA,KAAK,KAAK;sDAEV,cAAA,6LAAC;gDAAc,WAAU;;;;;;;;;;;sDAI3B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;wDACZ,KAAK,MAAM;sEACZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;4DAEV,SAAS;gEAAE,SAAS;4DAAI;4DACxB,SAAS;gEAAE,SAAS;4DAAE;4DACtB,YAAY;gEAAE,UAAU;4DAAI;sEAE3B,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,QAAQ,CAAC,MAAM;2DALxB,QAAQ,CAAC,MAAM;;;;;wDAOrB,KAAK,MAAM;;;;;;;8DAEd,6LAAC;oDAAG,WAAU;8DACX,KAAK,KAAK;;;;;;;;;;;;sDAKf,6LAAC;4CAAE,WAAU;sDACV,KAAK,WAAW;;;;;;;;;;;;;;;;;2BArClB,KAAK,KAAK;;;;;oBA2CrB;;;;;;8BAIF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mIAAA,CAAA,QAAK,CAAC,KAAK;gCAAC,WAAU;;;;;;0CACvB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlB;GAzJM;KAAA", "debugId": null}}, {"offset": {"line": 4455, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/components/ui/modal.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { createPortal } from \"react-dom\"\nimport { motion, AnimatePresence } from \"framer-motion\"\nimport { X } from \"lucide-react\"\nimport { cn } from \"@/utils\"\nimport { Button } from \"./button\"\n\nexport interface ModalProps {\n  isOpen: boolean\n  onClose: () => void\n  title?: string\n  description?: string\n  children: React.ReactNode\n  size?: \"sm\" | \"md\" | \"lg\" | \"xl\" | \"full\"\n  showCloseButton?: boolean\n  closeOnOverlayClick?: boolean\n  closeOnEscape?: boolean\n  className?: string\n}\n\nconst Modal: React.FC<ModalProps> = ({\n  isOpen,\n  onClose,\n  title,\n  description,\n  children,\n  size = \"md\",\n  showCloseButton = true,\n  closeOnOverlayClick = true,\n  closeOnEscape = true,\n  className,\n}) => {\n  const [mounted, setMounted] = React.useState(false)\n\n  React.useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  React.useEffect(() => {\n    if (!closeOnEscape) return\n\n    const handleEscape = (e: KeyboardEvent) => {\n      if (e.key === \"Escape\" && isOpen) {\n        onClose()\n      }\n    }\n\n    document.addEventListener(\"keydown\", handleEscape)\n    return () => document.removeEventListener(\"keydown\", handleEscape)\n  }, [isOpen, onClose, closeOnEscape])\n\n  React.useEffect(() => {\n    if (isOpen) {\n      document.body.style.overflow = \"hidden\"\n    } else {\n      document.body.style.overflow = \"unset\"\n    }\n\n    return () => {\n      document.body.style.overflow = \"unset\"\n    }\n  }, [isOpen])\n\n  const sizeClasses = {\n    sm: \"max-w-md\",\n    md: \"max-w-lg\",\n    lg: \"max-w-2xl\",\n    xl: \"max-w-4xl\",\n    full: \"max-w-[95vw] max-h-[95vh]\",\n  }\n\n  if (!mounted) return null\n\n  return createPortal(\n    <AnimatePresence>\n      {isOpen && (\n        <div className=\"fixed inset-0 z-50 flex items-center justify-center p-4\">\n          {/* Overlay */}\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"absolute inset-0 bg-black/50 backdrop-blur-sm\"\n            onClick={closeOnOverlayClick ? onClose : undefined}\n          />\n\n          {/* Modal */}\n          <motion.div\n            initial={{ opacity: 0, scale: 0.95, y: 20 }}\n            animate={{ opacity: 1, scale: 1, y: 0 }}\n            exit={{ opacity: 0, scale: 0.95, y: 20 }}\n            transition={{ type: \"spring\", duration: 0.3 }}\n            className={cn(\n              \"relative w-full bg-background rounded-lg shadow-xl border\",\n              sizeClasses[size],\n              className\n            )}\n            onClick={(e) => e.stopPropagation()}\n          >\n            {/* Header */}\n            {(title || showCloseButton) && (\n              <div className=\"flex items-center justify-between p-6 border-b\">\n                <div>\n                  {title && (\n                    <h2 className=\"text-lg font-semibold text-foreground\">\n                      {title}\n                    </h2>\n                  )}\n                  {description && (\n                    <p className=\"text-sm text-muted-foreground mt-1\">\n                      {description}\n                    </p>\n                  )}\n                </div>\n                {showCloseButton && (\n                  <Button\n                    variant=\"ghost\"\n                    size=\"icon\"\n                    onClick={onClose}\n                    className=\"h-8 w-8\"\n                  >\n                    <X className=\"h-4 w-4\" />\n                  </Button>\n                )}\n              </div>\n            )}\n\n            {/* Content */}\n            <div className=\"p-6\">{children}</div>\n          </motion.div>\n        </div>\n      )}\n    </AnimatePresence>,\n    document.body\n  )\n}\n\n// 确认对话框\nexport interface ConfirmModalProps {\n  isOpen: boolean\n  onClose: () => void\n  onConfirm: () => void\n  title: string\n  description?: string\n  confirmText?: string\n  cancelText?: string\n  variant?: \"default\" | \"destructive\"\n  loading?: boolean\n}\n\nconst ConfirmModal: React.FC<ConfirmModalProps> = ({\n  isOpen,\n  onClose,\n  onConfirm,\n  title,\n  description,\n  confirmText = \"确认\",\n  cancelText = \"取消\",\n  variant = \"default\",\n  loading = false,\n}) => {\n  return (\n    <Modal\n      isOpen={isOpen}\n      onClose={onClose}\n      title={title}\n      description={description}\n      size=\"sm\"\n    >\n      <div className=\"flex justify-end gap-3 mt-6\">\n        <Button variant=\"outline\" onClick={onClose} disabled={loading}>\n          {cancelText}\n        </Button>\n        <Button\n          variant={variant === \"destructive\" ? \"destructive\" : \"default\"}\n          onClick={onConfirm}\n          loading={loading}\n        >\n          {confirmText}\n        </Button>\n      </div>\n    </Modal>\n  )\n}\n\nexport { Modal, ConfirmModal }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;;;;;;;;;AAeA,MAAM,QAA8B,CAAC,EACnC,MAAM,EACN,OAAO,EACP,KAAK,EACL,WAAW,EACX,QAAQ,EACR,OAAO,IAAI,EACX,kBAAkB,IAAI,EACtB,sBAAsB,IAAI,EAC1B,gBAAgB,IAAI,EACpB,SAAS,EACV;;IACC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;2BAAE;YACd,WAAW;QACb;0BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;2BAAE;YACd,IAAI,CAAC,eAAe;YAEpB,MAAM;gDAAe,CAAC;oBACpB,IAAI,EAAE,GAAG,KAAK,YAAY,QAAQ;wBAChC;oBACF;gBACF;;YAEA,SAAS,gBAAgB,CAAC,WAAW;YACrC;mCAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;;QACvD;0BAAG;QAAC;QAAQ;QAAS;KAAc;IAEnC,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;2BAAE;YACd,IAAI,QAAQ;gBACV,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC,OAAO;gBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YAEA;mCAAO;oBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;0BAAG;QAAC;KAAO;IAEX,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,MAAM;IACR;IAEA,IAAI,CAAC,SAAS,OAAO;IAErB,qBAAO,CAAA,GAAA,oKAAA,CAAA,eAAY,AAAD,gBAChB,6LAAC,4LAAA,CAAA,kBAAe;kBACb,wBACC,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;oBACV,SAAS,sBAAsB,UAAU;;;;;;8BAI3C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,OAAO;wBAAM,GAAG;oBAAG;oBAC1C,SAAS;wBAAE,SAAS;wBAAG,OAAO;wBAAG,GAAG;oBAAE;oBACtC,MAAM;wBAAE,SAAS;wBAAG,OAAO;wBAAM,GAAG;oBAAG;oBACvC,YAAY;wBAAE,MAAM;wBAAU,UAAU;oBAAI;oBAC5C,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EACV,6DACA,WAAW,CAAC,KAAK,EACjB;oBAEF,SAAS,CAAC,IAAM,EAAE,eAAe;;wBAGhC,CAAC,SAAS,eAAe,mBACxB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;wCACE,uBACC,6LAAC;4CAAG,WAAU;sDACX;;;;;;wCAGJ,6BACC,6LAAC;4CAAE,WAAU;sDACV;;;;;;;;;;;;gCAIN,iCACC,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAOrB,6LAAC;4BAAI,WAAU;sCAAO;;;;;;;;;;;;;;;;;;;;;;cAK9B,SAAS,IAAI;AAEjB;GAnHM;KAAA;AAkIN,MAAM,eAA4C,CAAC,EACjD,MAAM,EACN,OAAO,EACP,SAAS,EACT,KAAK,EACL,WAAW,EACX,cAAc,IAAI,EAClB,aAAa,IAAI,EACjB,UAAU,SAAS,EACnB,UAAU,KAAK,EAChB;IACC,qBACE,6LAAC;QACC,QAAQ;QACR,SAAS;QACT,OAAO;QACP,aAAa;QACb,MAAK;kBAEL,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,qIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,SAAS;oBAAS,UAAU;8BACnD;;;;;;8BAEH,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAS,YAAY,gBAAgB,gBAAgB;oBACrD,SAAS;oBACT,SAAS;8BAER;;;;;;;;;;;;;;;;;AAKX;MAjCM", "debugId": null}}, {"offset": {"line": 4705, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/utils\"\n\nconst inputVariants = cva(\n  \"flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200\",\n  {\n    variants: {\n      variant: {\n        default: \"border-input hover:border-ring/50\",\n        error: \"border-error focus-visible:ring-error\",\n        success: \"border-success focus-visible:ring-success\",\n      },\n      size: {\n        default: \"h-10\",\n        sm: \"h-8 px-2 text-xs\",\n        lg: \"h-12 px-4 text-base\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement>,\n    VariantProps<typeof inputVariants> {\n  leftIcon?: React.ReactNode\n  rightIcon?: React.ReactNode\n  error?: string\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, variant, size, type, leftIcon, rightIcon, error, ...props }, ref) => {\n    const hasError = error || variant === \"error\"\n    \n    return (\n      <div className=\"relative\">\n        {leftIcon && (\n          <div className=\"absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground\">\n            {leftIcon}\n          </div>\n        )}\n        <input\n          type={type}\n          className={cn(\n            inputVariants({ \n              variant: hasError ? \"error\" : variant, \n              size, \n              className \n            }),\n            leftIcon && \"pl-10\",\n            rightIcon && \"pr-10\"\n          )}\n          ref={ref}\n          {...props}\n        />\n        {rightIcon && (\n          <div className=\"absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground\">\n            {rightIcon}\n          </div>\n        )}\n        {error && (\n          <p className=\"mt-1 text-xs text-error\">{error}</p>\n        )}\n      </div>\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input, inputVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,uXACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,OAAO;YACP,SAAS;QACX;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAWF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE;IACzE,MAAM,WAAW,SAAS,YAAY;IAEtC,qBACE,6LAAC;QAAI,WAAU;;YACZ,0BACC,6LAAC;gBAAI,WAAU;0BACZ;;;;;;0BAGL,6LAAC;gBACC,MAAM;gBACN,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EACV,cAAc;oBACZ,SAAS,WAAW,UAAU;oBAC9B;oBACA;gBACF,IACA,YAAY,SACZ,aAAa;gBAEf,KAAK;gBACJ,GAAG,KAAK;;;;;;YAEV,2BACC,6LAAC;gBAAI,WAAU;0BACZ;;;;;;YAGJ,uBACC,6LAAC;gBAAE,WAAU;0BAA2B;;;;;;;;;;;;AAIhD;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 4800, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/components/search/search-modal.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { motion, AnimatePresence } from \"framer-motion\"\nimport { Modal } from \"@/components/ui/modal\"\nimport { Input } from \"@/components/ui/input\"\nimport { Button } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Avatar, AvatarImage, AvatarFallback } from \"@/components/ui/avatar\"\nimport { Flex, Stack } from \"@/components/ui/layout\"\nimport { Icons } from \"@/components/ui/icon\"\nimport { cn, debounce, generateAppIcon } from \"@/utils\"\n\ninterface SearchResult {\n  id: string\n  type: \"app\" | \"activity\" | \"user\"\n  title: string\n  description: string\n  icon?: string\n  category?: string\n  badge?: string\n}\n\nconst mockResults: SearchResult[] = [\n  {\n    id: \"1\",\n    type: \"app\",\n    title: \"Notion\",\n    description: \"强大的笔记和协作工具\",\n    icon: generateAppIcon(\"Notion\", \"#000000\", 32),\n    category: \"效率工具\",\n  },\n  {\n    id: \"2\",\n    type: \"app\",\n    title: \"Figma\",\n    description: \"专业的UI/UX设计工具\",\n    icon: generateAppIcon(\"Figma\", \"#F24E1E\", 32),\n    category: \"设计工具\",\n  },\n  {\n    id: \"3\",\n    type: \"activity\",\n    title: \"Spotify Premium 邀请码\",\n    description: \"限时免费获取3个月会员\",\n    badge: \"热门\",\n  },\n  {\n    id: \"4\",\n    type: \"activity\",\n    title: \"Adobe Creative Cloud 学生优惠\",\n    description: \"学生专享50%折扣邀请码\",\n    badge: \"限时\",\n  },\n]\n\ninterface SearchModalProps {\n  isOpen: boolean\n  onClose: () => void\n}\n\nconst SearchModal: React.FC<SearchModalProps> = ({ isOpen, onClose }) => {\n  const [query, setQuery] = React.useState(\"\")\n  const [results, setResults] = React.useState<SearchResult[]>([])\n  const [isLoading, setIsLoading] = React.useState(false)\n  const [selectedIndex, setSelectedIndex] = React.useState(-1)\n\n  const inputRef = React.useRef<HTMLInputElement>(null)\n\n  // 搜索函数\n  const performSearch = React.useCallback(\n    debounce(async (searchQuery: string) => {\n      if (!searchQuery.trim()) {\n        setResults([])\n        return\n      }\n\n      setIsLoading(true)\n      \n      // 模拟API调用\n      await new Promise(resolve => setTimeout(resolve, 300))\n      \n      const filteredResults = mockResults.filter(result =>\n        result.title.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        result.description.toLowerCase().includes(searchQuery.toLowerCase())\n      )\n      \n      setResults(filteredResults)\n      setIsLoading(false)\n      setSelectedIndex(-1)\n    }, 300),\n    []\n  )\n\n  React.useEffect(() => {\n    performSearch(query)\n  }, [query, performSearch])\n\n  React.useEffect(() => {\n    if (isOpen && inputRef.current) {\n      inputRef.current.focus()\n    }\n  }, [isOpen])\n\n  // 键盘导航\n  React.useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      if (!isOpen) return\n\n      switch (e.key) {\n        case \"ArrowDown\":\n          e.preventDefault()\n          setSelectedIndex(prev => \n            prev < results.length - 1 ? prev + 1 : prev\n          )\n          break\n        case \"ArrowUp\":\n          e.preventDefault()\n          setSelectedIndex(prev => prev > 0 ? prev - 1 : -1)\n          break\n        case \"Enter\":\n          e.preventDefault()\n          if (selectedIndex >= 0 && results[selectedIndex]) {\n            handleResultClick(results[selectedIndex])\n          }\n          break\n      }\n    }\n\n    document.addEventListener(\"keydown\", handleKeyDown)\n    return () => document.removeEventListener(\"keydown\", handleKeyDown)\n  }, [isOpen, results, selectedIndex])\n\n  const handleResultClick = (result: SearchResult) => {\n    console.log(\"Navigate to:\", result)\n    onClose()\n    setQuery(\"\")\n    setResults([])\n  }\n\n  const getResultIcon = (result: SearchResult) => {\n    switch (result.type) {\n      case \"app\":\n        return Icons.app\n      case \"activity\":\n        return Icons.code\n      case \"user\":\n        return Icons.user\n      default:\n        return Icons.search\n    }\n  }\n\n  const getResultTypeLabel = (type: string) => {\n    switch (type) {\n      case \"app\":\n        return \"应用\"\n      case \"activity\":\n        return \"活动\"\n      case \"user\":\n        return \"用户\"\n      default:\n        return \"\"\n    }\n  }\n\n  return (\n    <Modal\n      isOpen={isOpen}\n      onClose={onClose}\n      size=\"lg\"\n      showCloseButton={false}\n      className=\"top-[10vh]\"\n    >\n      <div className=\"space-y-4\">\n        {/* 搜索输入框 */}\n        <div className=\"relative\">\n          <Input\n            ref={inputRef}\n            placeholder=\"搜索应用、活动或用户...\"\n            value={query}\n            onChange={(e) => setQuery(e.target.value)}\n            leftIcon={<Icons.search className=\"w-4 h-4\" />}\n            rightIcon={\n              isLoading ? (\n                <Icons.loading className=\"w-4 h-4 animate-spin\" />\n              ) : query ? (\n                <Button\n                  variant=\"ghost\"\n                  size=\"icon\"\n                  className=\"h-6 w-6\"\n                  onClick={() => setQuery(\"\")}\n                >\n                  <Icons.close className=\"w-3 h-3\" />\n                </Button>\n              ) : null\n            }\n            className=\"text-base\"\n          />\n        </div>\n\n        {/* 搜索结果 */}\n        <div className=\"max-h-96 overflow-y-auto\">\n          {query && !isLoading && results.length === 0 && (\n            <div className=\"text-center py-8 text-muted-foreground\">\n              <Icons.search className=\"w-12 h-12 mx-auto mb-4 opacity-50\" />\n              <p>没有找到相关结果</p>\n              <p className=\"text-sm\">尝试使用不同的关键词</p>\n            </div>\n          )}\n\n          {results.length > 0 && (\n            <Stack spacing=\"sm\">\n              <div className=\"text-sm text-muted-foreground px-2\">\n                找到 {results.length} 个结果\n              </div>\n              \n              {results.map((result, index) => {\n                const IconComponent = getResultIcon(result)\n                const isSelected = index === selectedIndex\n                \n                return (\n                  <motion.div\n                    key={result.id}\n                    initial={{ opacity: 0, y: 10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: index * 0.05 }}\n                    className={cn(\n                      \"flex items-center gap-3 p-3 rounded-lg cursor-pointer transition-colors\",\n                      isSelected \n                        ? \"bg-primary/10 border border-primary/20\" \n                        : \"hover:bg-muted\"\n                    )}\n                    onClick={() => handleResultClick(result)}\n                  >\n                    {result.icon ? (\n                      <Avatar size=\"sm\">\n                        <AvatarImage src={result.icon} alt={result.title} />\n                        <AvatarFallback>\n                          <IconComponent className=\"w-4 h-4\" />\n                        </AvatarFallback>\n                      </Avatar>\n                    ) : (\n                      <div className=\"flex items-center justify-center w-8 h-8 rounded-full bg-muted\">\n                        <IconComponent className=\"w-4 h-4\" />\n                      </div>\n                    )}\n                    \n                    <div className=\"flex-1 min-w-0\">\n                      <Flex align=\"center\" gap=\"sm\" className=\"mb-1\">\n                        <h4 className=\"font-medium truncate\">{result.title}</h4>\n                        <Badge variant=\"outline\" size=\"sm\">\n                          {getResultTypeLabel(result.type)}\n                        </Badge>\n                        {result.badge && (\n                          <Badge variant=\"warning\" size=\"sm\">\n                            {result.badge}\n                          </Badge>\n                        )}\n                      </Flex>\n                      <p className=\"text-sm text-muted-foreground truncate\">\n                        {result.description}\n                      </p>\n                      {result.category && (\n                        <p className=\"text-xs text-muted-foreground mt-1\">\n                          {result.category}\n                        </p>\n                      )}\n                    </div>\n                    \n                    <Icons.arrowRight className=\"w-4 h-4 text-muted-foreground\" />\n                  </motion.div>\n                )\n              })}\n            </Stack>\n          )}\n        </div>\n\n        {/* 快捷提示 */}\n        {!query && (\n          <div className=\"border-t pt-4\">\n            <div className=\"text-sm text-muted-foreground mb-3\">快捷搜索</div>\n            <div className=\"flex flex-wrap gap-2\">\n              {[\"Notion\", \"Figma\", \"Spotify\", \"Adobe\"].map((term) => (\n                <Button\n                  key={term}\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => setQuery(term)}\n                  className=\"text-xs\"\n                >\n                  {term}\n                </Button>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* 键盘提示 */}\n        <div className=\"border-t pt-4 text-xs text-muted-foreground\">\n          <Flex justify=\"between\">\n            <span>↑↓ 选择 • ↵ 确认 • ESC 关闭</span>\n            <span>⌘K 快速搜索</span>\n          </Flex>\n        </div>\n      </div>\n    </Modal>\n  )\n}\n\nexport { SearchModal }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAXA;;;;;;;;;;;AAuBA,MAAM,cAA8B;IAClC;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM,CAAA,GAAA,wHAAA,CAAA,kBAAe,AAAD,EAAE,UAAU,WAAW;QAC3C,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM,CAAA,GAAA,wHAAA,CAAA,kBAAe,AAAD,EAAE,SAAS,WAAW;QAC1C,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;IACT;CACD;AAOD,MAAM,cAA0C,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE;;IAClE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAkB,EAAE;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE,CAAC;IAE1D,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAY,AAAD,EAAoB;IAEhD,OAAO;IACP,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD,EACpC,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;kDAAE,OAAO;YACd,IAAI,CAAC,YAAY,IAAI,IAAI;gBACvB,WAAW,EAAE;gBACb;YACF;YAEA,aAAa;YAEb,UAAU;YACV,MAAM,IAAI;0DAAQ,CAAA,UAAW,WAAW,SAAS;;YAEjD,MAAM,kBAAkB,YAAY,MAAM;0EAAC,CAAA,SACzC,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC3D,OAAO,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;;YAGnE,WAAW;YACX,aAAa;YACb,iBAAiB,CAAC;QACpB;iDAAG,MACH,EAAE;IAGJ,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;iCAAE;YACd,cAAc;QAChB;gCAAG;QAAC;QAAO;KAAc;IAEzB,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;iCAAE;YACd,IAAI,UAAU,SAAS,OAAO,EAAE;gBAC9B,SAAS,OAAO,CAAC,KAAK;YACxB;QACF;gCAAG;QAAC;KAAO;IAEX,OAAO;IACP,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;iCAAE;YACd,MAAM;uDAAgB,CAAC;oBACrB,IAAI,CAAC,QAAQ;oBAEb,OAAQ,EAAE,GAAG;wBACX,KAAK;4BACH,EAAE,cAAc;4BAChB;uEAAiB,CAAA,OACf,OAAO,QAAQ,MAAM,GAAG,IAAI,OAAO,IAAI;;4BAEzC;wBACF,KAAK;4BACH,EAAE,cAAc;4BAChB;uEAAiB,CAAA,OAAQ,OAAO,IAAI,OAAO,IAAI,CAAC;;4BAChD;wBACF,KAAK;4BACH,EAAE,cAAc;4BAChB,IAAI,iBAAiB,KAAK,OAAO,CAAC,cAAc,EAAE;gCAChD,kBAAkB,OAAO,CAAC,cAAc;4BAC1C;4BACA;oBACJ;gBACF;;YAEA,SAAS,gBAAgB,CAAC,WAAW;YACrC;yCAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;;QACvD;gCAAG;QAAC;QAAQ;QAAS;KAAc;IAEnC,MAAM,oBAAoB,CAAC;QACzB,QAAQ,GAAG,CAAC,gBAAgB;QAC5B;QACA,SAAS;QACT,WAAW,EAAE;IACf;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ,OAAO,IAAI;YACjB,KAAK;gBACH,OAAO,mIAAA,CAAA,QAAK,CAAC,GAAG;YAClB,KAAK;gBACH,OAAO,mIAAA,CAAA,QAAK,CAAC,IAAI;YACnB,KAAK;gBACH,OAAO,mIAAA,CAAA,QAAK,CAAC,IAAI;YACnB;gBACE,OAAO,mIAAA,CAAA,QAAK,CAAC,MAAM;QACvB;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC,oIAAA,CAAA,QAAK;QACJ,QAAQ;QACR,SAAS;QACT,MAAK;QACL,iBAAiB;QACjB,WAAU;kBAEV,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;wBACJ,KAAK;wBACL,aAAY;wBACZ,OAAO;wBACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wBACxC,wBAAU,6LAAC,mIAAA,CAAA,QAAK,CAAC,MAAM;4BAAC,WAAU;;;;;;wBAClC,WACE,0BACE,6LAAC,mIAAA,CAAA,QAAK,CAAC,OAAO;4BAAC,WAAU;;;;;qCACvB,sBACF,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,SAAS;sCAExB,cAAA,6LAAC,mIAAA,CAAA,QAAK,CAAC,KAAK;gCAAC,WAAU;;;;;;;;;;qCAEvB;wBAEN,WAAU;;;;;;;;;;;8BAKd,6LAAC;oBAAI,WAAU;;wBACZ,SAAS,CAAC,aAAa,QAAQ,MAAM,KAAK,mBACzC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,mIAAA,CAAA,QAAK,CAAC,MAAM;oCAAC,WAAU;;;;;;8CACxB,6LAAC;8CAAE;;;;;;8CACH,6LAAC;oCAAE,WAAU;8CAAU;;;;;;;;;;;;wBAI1B,QAAQ,MAAM,GAAG,mBAChB,6LAAC,qIAAA,CAAA,QAAK;4BAAC,SAAQ;;8CACb,6LAAC;oCAAI,WAAU;;wCAAqC;wCAC9C,QAAQ,MAAM;wCAAC;;;;;;;gCAGpB,QAAQ,GAAG,CAAC,CAAC,QAAQ;oCACpB,MAAM,gBAAgB,cAAc;oCACpC,MAAM,aAAa,UAAU;oCAE7B,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO,QAAQ;wCAAK;wCAClC,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EACV,2EACA,aACI,2CACA;wCAEN,SAAS,IAAM,kBAAkB;;4CAEhC,OAAO,IAAI,iBACV,6LAAC,qIAAA,CAAA,SAAM;gDAAC,MAAK;;kEACX,6LAAC,qIAAA,CAAA,cAAW;wDAAC,KAAK,OAAO,IAAI;wDAAE,KAAK,OAAO,KAAK;;;;;;kEAChD,6LAAC,qIAAA,CAAA,iBAAc;kEACb,cAAA,6LAAC;4DAAc,WAAU;;;;;;;;;;;;;;;;qEAI7B,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAc,WAAU;;;;;;;;;;;0DAI7B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qIAAA,CAAA,OAAI;wDAAC,OAAM;wDAAS,KAAI;wDAAK,WAAU;;0EACtC,6LAAC;gEAAG,WAAU;0EAAwB,OAAO,KAAK;;;;;;0EAClD,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAU,MAAK;0EAC3B,mBAAmB,OAAO,IAAI;;;;;;4DAEhC,OAAO,KAAK,kBACX,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAU,MAAK;0EAC3B,OAAO,KAAK;;;;;;;;;;;;kEAInB,6LAAC;wDAAE,WAAU;kEACV,OAAO,WAAW;;;;;;oDAEpB,OAAO,QAAQ,kBACd,6LAAC;wDAAE,WAAU;kEACV,OAAO,QAAQ;;;;;;;;;;;;0DAKtB,6LAAC,mIAAA,CAAA,QAAK,CAAC,UAAU;gDAAC,WAAU;;;;;;;uCA/CvB,OAAO,EAAE;;;;;gCAkDpB;;;;;;;;;;;;;gBAML,CAAC,uBACA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAAqC;;;;;;sCACpD,6LAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAU;gCAAS;gCAAW;6BAAQ,CAAC,GAAG,CAAC,CAAC,qBAC5C,6LAAC,qIAAA,CAAA,SAAM;oCAEL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,SAAS;oCACxB,WAAU;8CAET;mCANI;;;;;;;;;;;;;;;;8BAcf,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,qIAAA,CAAA,OAAI;wBAAC,SAAQ;;0CACZ,6LAAC;0CAAK;;;;;;0CACN,6LAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlB;GAvPM;KAAA", "debugId": null}}, {"offset": {"line": 5322, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/components/ui/toast.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { createPortal } from \"react-dom\"\nimport { motion, AnimatePresence } from \"framer-motion\"\nimport { X, CheckCircle, AlertCircle, Info, AlertTriangle } from \"lucide-react\"\nimport { cn } from \"@/utils\"\nimport { But<PERSON> } from \"./button\"\n\nexport type ToastType = \"success\" | \"error\" | \"warning\" | \"info\"\n\nexport interface Toast {\n  id: string\n  type: ToastType\n  title: string\n  description?: string\n  duration?: number\n  action?: {\n    label: string\n    onClick: () => void\n  }\n}\n\ninterface ToastProps extends Toast {\n  onClose: (id: string) => void\n}\n\nconst ToastComponent: React.FC<ToastProps> = ({\n  id,\n  type,\n  title,\n  description,\n  duration = 5000,\n  action,\n  onClose,\n}) => {\n  React.useEffect(() => {\n    if (duration > 0) {\n      const timer = setTimeout(() => {\n        onClose(id)\n      }, duration)\n\n      return () => clearTimeout(timer)\n    }\n  }, [id, duration, onClose])\n\n  const icons = {\n    success: CheckCircle,\n    error: AlertCircle,\n    warning: <PERSON><PERSON><PERSON>riangle,\n    info: Info,\n  }\n\n  const Icon = icons[type]\n\n  const variants = {\n    success: \"border-success/20 bg-success/10 text-success-foreground\",\n    error: \"border-error/20 bg-error/10 text-error-foreground\",\n    warning: \"border-warning/20 bg-warning/10 text-warning-foreground\",\n    info: \"border-info/20 bg-info/10 text-info-foreground\",\n  }\n\n  const iconColors = {\n    success: \"text-success\",\n    error: \"text-error\",\n    warning: \"text-warning\",\n    info: \"text-info\",\n  }\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 50, scale: 0.3 }}\n      animate={{ opacity: 1, y: 0, scale: 1 }}\n      exit={{ opacity: 0, scale: 0.5, transition: { duration: 0.2 } }}\n      className={cn(\n        \"relative flex w-full items-center space-x-4 rounded-md border p-4 shadow-lg backdrop-blur-sm\",\n        variants[type]\n      )}\n    >\n      <Icon className={cn(\"h-5 w-5 flex-shrink-0\", iconColors[type])} />\n      \n      <div className=\"flex-1 space-y-1\">\n        <p className=\"text-sm font-medium\">{title}</p>\n        {description && (\n          <p className=\"text-sm opacity-90\">{description}</p>\n        )}\n      </div>\n\n      {action && (\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={action.onClick}\n          className=\"h-8 px-3\"\n        >\n          {action.label}\n        </Button>\n      )}\n\n      <Button\n        variant=\"ghost\"\n        size=\"icon\"\n        onClick={() => onClose(id)}\n        className=\"h-8 w-8 opacity-70 hover:opacity-100\"\n      >\n        <X className=\"h-4 w-4\" />\n      </Button>\n    </motion.div>\n  )\n}\n\n// Toast容器\ninterface ToastContainerProps {\n  toasts: Toast[]\n  onClose: (id: string) => void\n}\n\nconst ToastContainer: React.FC<ToastContainerProps> = ({ toasts, onClose }) => {\n  const [mounted, setMounted] = React.useState(false)\n\n  React.useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  if (!mounted) return null\n\n  return createPortal(\n    <div className=\"fixed top-4 right-4 z-50 flex flex-col space-y-2 w-full max-w-sm\">\n      <AnimatePresence>\n        {toasts.map((toast) => (\n          <ToastComponent\n            key={toast.id}\n            {...toast}\n            onClose={onClose}\n          />\n        ))}\n      </AnimatePresence>\n    </div>,\n    document.body\n  )\n}\n\n// Toast Hook\ninterface ToastContextType {\n  toasts: Toast[]\n  addToast: (toast: Omit<Toast, \"id\">) => void\n  removeToast: (id: string) => void\n  clearToasts: () => void\n}\n\nconst ToastContext = React.createContext<ToastContextType | undefined>(undefined)\n\nexport const ToastProvider: React.FC<{ children: React.ReactNode }> = ({\n  children,\n}) => {\n  const [toasts, setToasts] = React.useState<Toast[]>([])\n\n  const addToast = React.useCallback((toast: Omit<Toast, \"id\">) => {\n    const id = Math.random().toString(36).substring(2, 15)\n    setToasts((prev) => [...prev, { ...toast, id }])\n  }, [])\n\n  const removeToast = React.useCallback((id: string) => {\n    setToasts((prev) => prev.filter((toast) => toast.id !== id))\n  }, [])\n\n  const clearToasts = React.useCallback(() => {\n    setToasts([])\n  }, [])\n\n  return (\n    <ToastContext.Provider\n      value={{ toasts, addToast, removeToast, clearToasts }}\n    >\n      {children}\n      <ToastContainer toasts={toasts} onClose={removeToast} />\n    </ToastContext.Provider>\n  )\n}\n\nexport const useToast = () => {\n  const context = React.useContext(ToastContext)\n  if (context === undefined) {\n    throw new Error(\"useToast must be used within a ToastProvider\")\n  }\n\n  return {\n    ...context,\n    success: (title: string, description?: string) =>\n      context.addToast({ type: \"success\", title, description }),\n    error: (title: string, description?: string) =>\n      context.addToast({ type: \"error\", title, description }),\n    warning: (title: string, description?: string) =>\n      context.addToast({ type: \"warning\", title, description }),\n    info: (title: string, description?: string) =>\n      context.addToast({ type: \"info\", title, description }),\n  }\n}\n\nexport { ToastContainer }\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;;;;;;;AAoBA,MAAM,iBAAuC,CAAC,EAC5C,EAAE,EACF,IAAI,EACJ,KAAK,EACL,WAAW,EACX,WAAW,IAAI,EACf,MAAM,EACN,OAAO,EACR;;IACC,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;oCAAE;YACd,IAAI,WAAW,GAAG;gBAChB,MAAM,QAAQ;sDAAW;wBACvB,QAAQ;oBACV;qDAAG;gBAEH;gDAAO,IAAM,aAAa;;YAC5B;QACF;mCAAG;QAAC;QAAI;QAAU;KAAQ;IAE1B,MAAM,QAAQ;QACZ,SAAS,8NAAA,CAAA,cAAW;QACpB,OAAO,uNAAA,CAAA,cAAW;QAClB,SAAS,2NAAA,CAAA,gBAAa;QACtB,MAAM,qMAAA,CAAA,OAAI;IACZ;IAEA,MAAM,OAAO,KAAK,CAAC,KAAK;IAExB,MAAM,WAAW;QACf,SAAS;QACT,OAAO;QACP,SAAS;QACT,MAAM;IACR;IAEA,MAAM,aAAa;QACjB,SAAS;QACT,OAAO;QACP,SAAS;QACT,MAAM;IACR;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;YAAI,OAAO;QAAI;QACzC,SAAS;YAAE,SAAS;YAAG,GAAG;YAAG,OAAO;QAAE;QACtC,MAAM;YAAE,SAAS;YAAG,OAAO;YAAK,YAAY;gBAAE,UAAU;YAAI;QAAE;QAC9D,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EACV,gGACA,QAAQ,CAAC,KAAK;;0BAGhB,6LAAC;gBAAK,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB,UAAU,CAAC,KAAK;;;;;;0BAE7D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAAuB;;;;;;oBACnC,6BACC,6LAAC;wBAAE,WAAU;kCAAsB;;;;;;;;;;;;YAItC,wBACC,6LAAC,qIAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,SAAS,OAAO,OAAO;gBACvB,WAAU;0BAET,OAAO,KAAK;;;;;;0BAIjB,6LAAC,qIAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,SAAS,IAAM,QAAQ;gBACvB,WAAU;0BAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAIrB;GAlFM;KAAA;AA0FN,MAAM,iBAAgD,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE;;IACxE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;oCAAE;YACd,WAAW;QACb;mCAAG,EAAE;IAEL,IAAI,CAAC,SAAS,OAAO;IAErB,qBAAO,CAAA,GAAA,oKAAA,CAAA,eAAY,AAAD,gBAChB,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,4LAAA,CAAA,kBAAe;sBACb,OAAO,GAAG,CAAC,CAAC,sBACX,6LAAC;oBAEE,GAAG,KAAK;oBACT,SAAS;mBAFJ,MAAM,EAAE;;;;;;;;;;;;;;cAOrB,SAAS,IAAI;AAEjB;IAvBM;MAAA;AAiCN,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAgC;AAEhE,MAAM,gBAAyD,CAAC,EACrE,QAAQ,EACT;;IACC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAW,EAAE;IAEtD,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;+CAAE,CAAC;YAClC,MAAM,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;YACnD;uDAAU,CAAC,OAAS;2BAAI;wBAAM;4BAAE,GAAG,KAAK;4BAAE;wBAAG;qBAAE;;QACjD;8CAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;kDAAE,CAAC;YACrC;0DAAU,CAAC,OAAS,KAAK,MAAM;kEAAC,CAAC,QAAU,MAAM,EAAE,KAAK;;;QAC1D;iDAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;kDAAE;YACpC,UAAU,EAAE;QACd;iDAAG,EAAE;IAEL,qBACE,6LAAC,aAAa,QAAQ;QACpB,OAAO;YAAE;YAAQ;YAAU;YAAa;QAAY;;YAEnD;0BACD,6LAAC;gBAAe,QAAQ;gBAAQ,SAAS;;;;;;;;;;;;AAG/C;IA1Ba;MAAA;AA4BN,MAAM,WAAW;;IACtB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;IACjC,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;QACL,GAAG,OAAO;QACV,SAAS,CAAC,OAAe,cACvB,QAAQ,QAAQ,CAAC;gBAAE,MAAM;gBAAW;gBAAO;YAAY;QACzD,OAAO,CAAC,OAAe,cACrB,QAAQ,QAAQ,CAAC;gBAAE,MAAM;gBAAS;gBAAO;YAAY;QACvD,SAAS,CAAC,OAAe,cACvB,QAAQ,QAAQ,CAAC;gBAAE,MAAM;gBAAW;gBAAO;YAAY;QACzD,MAAM,CAAC,OAAe,cACpB,QAAQ,QAAQ,CAAC;gBAAE,MAAM;gBAAQ;gBAAO;YAAY;IACxD;AACF;IAjBa", "debugId": null}}, {"offset": {"line": 5611, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitRepository/ICBar-AllInOne/frontend/src/app/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { Header } from \"@/components/layout/header\"\nimport { Footer } from \"@/components/layout/footer\"\nimport { <PERSON> } from \"@/components/sections/hero\"\nimport { Features } from \"@/components/sections/features\"\nimport { AppShowcase } from \"@/components/sections/app-showcase\"\nimport { Stats } from \"@/components/sections/stats\"\nimport { SearchModal } from \"@/components/search/search-modal\"\nimport { ToastProvider } from \"@/components/ui/toast\"\n\nexport default function Home() {\n  const [isSearchOpen, setIsSearchOpen] = React.useState(false)\n\n  // 全局快捷键\n  React.useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      if ((e.metaKey || e.ctrlKey) && e.key === \"k\") {\n        e.preventDefault()\n        setIsSearchOpen(true)\n      }\n    }\n\n    document.addEventListener(\"keydown\", handleKeyDown)\n    return () => document.removeEventListener(\"keydown\", handleKeyDown)\n  }, [])\n\n  return (\n    <ToastProvider>\n      <div className=\"min-h-screen bg-background\">\n        {/* 导航栏 */}\n        <Header />\n\n        {/* 主要内容 */}\n        <main>\n          {/* 英雄区 */}\n          <Hero />\n\n          {/* 特色功能 */}\n          <Features />\n\n          {/* 应用推荐 */}\n          <AppShowcase />\n\n          {/* 统计数据 */}\n          <Stats />\n        </main>\n\n        {/* 页脚 */}\n        <Footer />\n\n        {/* 搜索模态框 */}\n        <SearchModal\n          isOpen={isSearchOpen}\n          onClose={() => setIsSearchOpen(false)}\n        />\n      </div>\n    </ToastProvider>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;AAYe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IAEvD,QAAQ;IACR,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;0BAAE;YACd,MAAM;gDAAgB,CAAC;oBACrB,IAAI,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,KAAK,EAAE,GAAG,KAAK,KAAK;wBAC7C,EAAE,cAAc;wBAChB,gBAAgB;oBAClB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,WAAW;YACrC;kCAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;;QACvD;yBAAG,EAAE;IAEL,qBACE,6LAAC,oIAAA,CAAA,gBAAa;kBACZ,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,yIAAA,CAAA,SAAM;;;;;8BAGP,6LAAC;;sCAEC,6LAAC,yIAAA,CAAA,OAAI;;;;;sCAGL,6LAAC,6IAAA,CAAA,WAAQ;;;;;sCAGT,6LAAC,oJAAA,CAAA,cAAW;;;;;sCAGZ,6LAAC,0IAAA,CAAA,QAAK;;;;;;;;;;;8BAIR,6LAAC,yIAAA,CAAA,SAAM;;;;;8BAGP,6LAAC,kJAAA,CAAA,cAAW;oBACV,QAAQ;oBACR,SAAS,IAAM,gBAAgB;;;;;;;;;;;;;;;;;AAKzC;GAhDwB;KAAA", "debugId": null}}]}